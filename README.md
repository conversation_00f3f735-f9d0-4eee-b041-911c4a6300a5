# Resume Tailor - AI-Powered Resume Optimization

An AI-powered application that tailors your resume for specific job applications to maximize your interview chances.

## Features

- **Smart Resume Tailoring**: Analyzes job descriptions and optimizes your resume content
- **Match Score**: Provides a numerical score indicating alignment with the job description
- **Strategic Rationale**: Explains the key strategic choices made during optimization
- **Multiple Input Methods**: Paste text directly or upload PDF/text files
- **PDF Support**: Upload your resume in PDF format for automatic text extraction
- **Export Options**: Download as Markdown or copy to clipboard
- **Real-time Preview**: See both formatted and raw markdown output
- **File Validation**: Supports PDF and text files up to 10MB

## Architecture

- **Frontend**: Next.js 14 with React
- **Backend**: Flask (Python)
- **AI Engine**: Custom resume generation algorithm (in `resume_generator.py`)

## Setup Instructions

### Prerequisites

- Node.js 18+ and npm
- Python 3.8+
- pip

### Backend Setup

1. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Start the Flask backend**:
   ```bash
   cd web_app
   python app.py
   ```
   
   The backend will run on `http://localhost:8080`

### Frontend Setup

1. **Install Node.js dependencies**:
   ```bash
   npm install
   ```

2. **Start the Next.js development server**:
   ```bash
   npm run dev
   ```
   
   The frontend will run on `http://localhost:3000`

## Usage

1. **Open the application** in your browser at `http://localhost:3000`

2. **Fill in the form**:
   - **Job Description**: Paste the full job posting
   - **Your Current Resume**: Paste your resume content or upload a PDF/text file
   - **Key Achievements** (Optional): List 1-3 key achievements to highlight
   - **Specific Concerns** (Optional): Note any areas you want to improve

3. **Generate your tailored resume** by clicking the "Generate Tailored Resume" button

4. **Review the results**:
   - Match score and rationale
   - Strategic rationale explaining key decisions
   - Formatted resume preview
   - Raw markdown for copying

5. **Export your resume**:
   - Copy to clipboard for pasting elsewhere
   - Download as Markdown file

## Project Structure

```
├── app/                    # Next.js frontend
│   ├── api/               # API routes
│   ├── page.js            # Main page component
│   ├── page.module.css    # Styles
│   ├── layout.js          # Root layout
│   └── globals.css        # Global styles
├── web_app/               # Flask backend
│   └── app.py            # Flask application
├── resume_generator.py    # Core AI logic
├── PRD.md                # Product Requirements Document
├── package.json          # Node.js dependencies
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## Development

### Adding New Features

1. **Frontend changes**: Modify files in the `app/` directory
2. **Backend changes**: Modify `web_app/app.py` or `resume_generator.py`
3. **AI improvements**: Update the logic in `resume_generator.py`

### API Endpoints

- `POST /generate`: Generates a tailored resume
  - Input: `job_description`, `base_resume_content`, `key_achievements`, `specific_concerns`
  - Output: `tailored_resume_markdown`, `match_score`, `match_rationale`, `strategic_rationale`

- `POST /upload`: Extracts text from uploaded files
  - Input: FormData with `file` (PDF or text file, max 10MB)
  - Output: `text` (extracted content)

## Troubleshooting

### Common Issues

1. **CORS errors**: Make sure the Flask backend is running with CORS enabled
2. **Port conflicts**: Ensure ports 3000 and 8080 are available
3. **Module not found**: Check that all dependencies are installed

### Backend not responding

1. Check that the Flask server is running on port 8080
2. Verify the API endpoint URL in `app/api/generate/route.js`

### Frontend build issues

1. Run `npm install` to ensure all dependencies are installed
2. Check for any TypeScript or linting errors

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for educational and personal use.
