
## **PRODUCT REQUIREMENTS DOCUMENT (PRD): Elite Resume Generation (v2 - Layout Specific)**

**1. Overview & Goal**
This document outlines the requirements for an AI-powered resume generation system. The primary goal is to produce a personalized, highly tailored resume that significantly maximizes the user's interview chances for a specific job, aiming for a "top 0.1%" quality that excels in both Applicant Tracking Systems (ATS) and human reviews.

**2. User Profile**
The user is a professional seeking to optimize their resume for a specific job application. They will provide their existing resume content and the target job description.

**3. Inputs Required from User**

*   **A. Target Job Description:**
    *   **Requirement:** Full job advertisement text or a direct, accessible link to the job posting.
    *   **Purpose:** To understand the target role, required skills, experience, company culture, and keywords.
*   **B. User's Base Resume Content:**
    *   **Requirement:** Plain text content of the user's current resume. The OCR'd content from the provided resume images (timestamp: `[timestamp of your resume image upload, e.g., "2023-10-27 10:00 UTC"]`) serves as this input.
    *   **Purpose:** To understand the user's experience, skills, achievements, and existing narrative.
*   **C. Resume Layout Preference & Constraints (Adapted for User-Provided Visual):**
    *   **Requirement:** The AI must adhere as closely as possible to the structure, section ordering (Summary, Work Experience, Skills, Languages, Education), font weight usage (bolding for titles, company names), and general visual layout demonstrated in the user-provided resume images (referenced in 3.B).
        *   This includes the style of headings (e.g., "WORK EXPERIENCE"), bullet point presentation, and the formatting of job titles, company names/locations, and dates (e.g., "01/2025 - Present" format, though dates should be factually corrected if needed, e.g., for future start dates).
        *   The aim is to populate the user's established visual structure with strategically rewritten and enhanced content.
        *   Maintain clean, ATS-compliant practices: no tables, no multi-column text layouts (unless the original single-column structure is preserved), no images or icons. Standard bullet characters.
    *   **Constraint:** Maximum 2 pages (as per the provided example).
*   **D. Key Achievements/Projects (Optional but Recommended for User to Specify for Max Impact):**
    *   **Requirement:** User can list 1-3 specific achievements or projects from their base resume they believe are *most* crucial to highlight for the *target job*.
    *   **Purpose:** Helps the AI prioritize and strategically feature these aspects, especially for the "Signature Project Highlight."
*   **E. Specific Concerns or Areas for Improvement (Optional for User to Specify):**
    *   **Requirement:** User can note any specific parts of their resume they are unhappy with or specific aspects of the job they want to ensure are emphasized beyond what's obvious in the JD.
    *   **Purpose:** Allows for more targeted refinement by the AI.

**4. Core AI Strategy & Guiding Principles**
The AI must apply the following elite principles to transform the base resume into a job-winning document:

*   **4.1. Strategic Positioning:**
    *   Go beyond keyword matching. Position the user as a proactive, strategic contributor aligned with the target company’s mission, values, and current needs (as inferred from the JD and company research if possible).
    *   This strategic angle must be powerfully articulated in the resume summary/header (currently "Al Consultant | Digital Product Strategist | Account Manager" followed by a paragraph) using confident, compelling language.
*   **4.2. Contextual Proof & Quantifiable Impact (The "CAR + Why"):**
    *   Every significant bullet point, especially in the "WORK EXPERIENCE" section, must clearly articulate:
        *   **C**ontext/Challenge: What was the situation or problem?
        *   **A**ction: What specific actions did the user take?
        *   **R**esult: What was the quantifiable or clear qualitative outcome? (Prioritize metrics: %, $, #). If exact numbers are unavailable, credible estimates or strong qualitative impacts are acceptable.
        *   **Why it Mattered:** What was the broader business significance or strategic value of this achievement for the team/company?
*   **4.3. Authentic Human Voice:**
    *   Maintain a natural, confident, and professional tone, consistent with the existing resume's direct style.
    *   Avoid generic, clichéd AI phrases (e.g., "highly skilled," "results-oriented," "proven track record," "dynamic professional").
    *   Inject subtle personal touches or unique phrasings where appropriate and aligned with the user's likely voice and the target company culture (e.g., the existing "I help companies automate workflows... I work at the intersection of design, data, and execution" is a good base to build upon).
*   **4.4. Cultural & Linguistic Alignment:**
    *   Analyze the tone, language, and values evident in the job description and (if possible via quick search) the target company's public communications (website, LinkedIn).
    *   Mirror this energy, terminology, and professional style in the resume, particularly in the summary and project descriptions, while staying true to the user's professional persona.
*   **4.5. Signature Project/Achievement Highlight:**
    *   Identify one standout project or achievement from the user's experience (ideally from user's input 3.D, or selected by AI if not specified) that most strongly aligns with the critical requirements of the target job.
    *   Highlight this clearly. Given the current resume structure, this could be:
        *   A bolded introductory line within the relevant experience entry (e.g., "**Signature Project: Led development of an AI assistant for a German telecom, automating content creation and reducing delivery time by 60%.**").
        *   A distinct call-out within the main summary paragraph.
    *   The choice of placement should maximize visibility and relevance within the existing layout.
*   **4.6. Optimized Section Headers & Structure:**
    *   The primary section headers ("WORK EXPERIENCE," "SKILLS," "LANGUAGES," "EDUCATION") should be maintained as per the provided resume layout (Input 3.C).
    *   The AI should ensure the *content within* these sections flows logically and prioritizes information most relevant to the target job.
*   **4.7. Embedded Framework/Philosophy (Optional but Impactful):**
    *   If discernible from the base resume or if it naturally fits, consider articulating a concise (1-line) unique working philosophy, methodology, or framework the user applies.
    *   Example: "My approach: Blend UX, AI, and client strategy to reduce manual work and unlock measurable growth." (Could be a refined version of the existing summary sentence).
    *   This could be a subtle final sentence in the summary or a micro-signature if a footer were added (though the current resume doesn't have one). Prioritize integration into the summary.

**5. Output Requirements & Format**

*   **5.1. Format:**
    *   **Primary:** Markdown for easy editing, review, and ATS compatibility.
    *   Clear use of Markdown for headers (matching the visual hierarchy of the original, e.g., `##` for major sections, `###` or `####` for job titles if appropriate), bold (**text**), and bullet points (* or -).
*   **5.2. Resume Structure (Adhering to User's Provided Layout):**
    1.  **Contact Information:** (Name, Pufendorfstrasse 7A, Berlin • +************* • <EMAIL> • linkedin.com/in/alexguyenne)
    2.  **Title Line:** (Al Consultant | Digital Product Strategist | Account Manager)
    3.  **Summary Paragraph:** (Tailored, incorporating 4.1, potentially 4.5 or 4.7)
    4.  **WORK EXPERIENCE:** (Reverse chronological, Company Name, Location, Job Title, Dates, Bullets per 4.2. Ensure date consistency, e.g., "01/2025 - Present" for Gladtobe should be verified/corrected to a current or past date like "01/2024 - Present" or actual start if very recent, to avoid confusion unless explicitly stated as a future confirmed role.)
    5.  **SKILLS:** (Categorized if possible, tailored to JD keywords, substantiated by experience)
    6.  **LANGUAGES:** (As listed)
    7.  **EDUCATION:** (Degree, University, Location, Dates)
*   **5.3. Content & Style:**
    *   Every line must be tailored – no generic content.
    *   Concise, clear, and skimmable. Use strong action verbs.
    *   ATS-safe as per 3.C.
    *   Font weight usage (bolding) and bullet spacing should mirror the provided resume's style.
*   **5.4. Deliverables:**
    1.  **The Tailored Resume:** In Markdown format, reflecting the described structure and content.
    2.  **Match Score & Rationale:**
        *   A numerical score (e.g., 85/100) indicating the perceived alignment of the generated resume with the job description.
        *   A brief (2-3 lines) explanation highlighting:
            *   Key improvements made based on the PRD principles.
            *   Areas where further personalization by the user might be beneficial (e.g., if certain metrics were estimated and the user has exact figures, or if specific anecdotes could strengthen a point).
    3.  **Strategic Rationale (Brief):** A short paragraph explaining 2-3 key strategic choices made during the rewrite (e.g., why a certain project was highlighted, how the summary was rephrased to align with the JD, how the company's culture (if discernible) was reflected).

**6. Non-Functional Requirements**
*   **Performance:** Generation should be reasonably fast.
*   **Reliability:** The AI should consistently apply the guiding principles.
*   **No Hallucinations:** All information must be derived from the user's input and the JD. No fabricating experience or skills.

**7. Success Metrics**
*   Primary: User satisfaction, leading to increased interview callbacks (long-term measure).
*   Secondary: Clarity, conciseness, and professional quality of the generated resume. Adherence to ATS best practices and user's layout. Positive feedback on the "Match Score & Rationale."

---