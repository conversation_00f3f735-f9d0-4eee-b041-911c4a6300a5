from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import sys
import os
import io
import PyPDF2

# Add the parent directory to the sys.path to import resume_generator
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from resume_generator import generate_resume

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

@app.route('/')
def index():
    return jsonify({'message': 'Resume Tailor API is running', 'endpoints': ['/generate', '/upload']})

@app.route('/upload', methods=['POST'])
def upload_file():
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Check file size (max 10MB)
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > 10 * 1024 * 1024:  # 10MB
            return jsonify({'error': 'File size too large. Maximum size is 10MB.'}), 400

        # Extract text based on file type
        filename = file.filename.lower()
        extracted_text = ''

        if filename.endswith('.pdf'):
            extracted_text = extract_text_from_pdf(file)
        elif filename.endswith('.txt'):
            extracted_text = file.read().decode('utf-8')
        else:
            return jsonify({'error': 'Unsupported file type. Please upload a PDF or text file.'}), 400

        if not extracted_text.strip():
            return jsonify({'error': 'No text content found in the file.'}), 400

        return jsonify({'text': extracted_text})

    except Exception as e:
        app.logger.error(f'Error processing file: {str(e)}')
        return jsonify({'error': 'Failed to process file'}), 500

def extract_text_from_pdf(file):
    """Extract text from PDF file using PyPDF2"""
    try:
        pdf_reader = PyPDF2.PdfReader(file)
        text = ''

        for page in pdf_reader.pages:
            text += page.extract_text() + '\n'

        # Clean up the text
        text = ' '.join(text.split())  # Remove extra whitespace
        return text

    except Exception as e:
        raise Exception(f'Failed to extract text from PDF: {str(e)}')

@app.route('/generate', methods=['POST'])
def generate():
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        job_description = data.get('job_description', '').strip()
        base_resume_content = data.get('base_resume_content', '').strip()
        key_achievements = data.get('key_achievements', '').strip()
        specific_concerns = data.get('specific_concerns', '').strip()

        # Validation
        if not job_description:
            return jsonify({'error': 'Job description is required'}), 400

        if not base_resume_content:
            return jsonify({'error': 'Base resume content is required'}), 400

        # Convert key_achievements to list if it's a string
        if isinstance(key_achievements, str) and key_achievements:
            key_achievements = [achievement.strip() for achievement in key_achievements.split('\n') if achievement.strip()]
        elif not key_achievements:
            key_achievements = []

        result = generate_resume(
            job_description=job_description,
            base_resume_content=base_resume_content,
            key_achievements=key_achievements,
            specific_concerns=specific_concerns
        )

        return jsonify(result)

    except ValueError as e:
        return jsonify({'error': f'Invalid input: {str(e)}'}), 400
    except Exception as e:
        app.logger.error(f'Error generating resume: {str(e)}')
        return jsonify({'error': 'An internal error occurred while generating the resume'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8080)