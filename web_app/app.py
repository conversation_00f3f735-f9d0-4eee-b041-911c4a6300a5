from flask import Flask, request, jsonify, render_template
import sys
import os

# Add the parent directory to the sys.path to import resume_generator
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from resume_generator import generate_resume

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate():
    data = request.get_json()
    job_description = data.get('job_description')
    base_resume_content = data.get('base_resume_content')
    key_achievements = data.get('key_achievements', '')

    if not job_description or not base_resume_content:
        return jsonify({'error': 'Job description and base resume content are required.'}), 400

    try:
        result = generate_resume(job_description, base_resume_content, key_achievements)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8080)