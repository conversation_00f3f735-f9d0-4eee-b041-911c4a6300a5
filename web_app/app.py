from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import sys
import os
import io
import re
import PyPDF2

# Add the parent directory to the sys.path to import resume_generator
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from resume_generator import generate_resume

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

@app.route('/')
def index():
    return jsonify({'message': 'Resume Tailor API is running', 'endpoints': ['/generate', '/upload', '/debug-parse']})

@app.route('/debug-parse', methods=['POST'])
def debug_parse():
    """Debug endpoint to see how resume content is being parsed"""
    try:
        data = request.get_json()
        content = data.get('content', '')

        if not content:
            return jsonify({'error': 'No content provided'}), 400

        # Import the parsing function
        from resume_generator import parse_resume_content

        # Parse the content
        parsed = parse_resume_content(content)

        # Return detailed parsing results
        return jsonify({
            'original_content': content,
            'parsed_sections': parsed,
            'content_lines': content.split('\n')[:20],  # First 20 lines for debugging
            'total_lines': len(content.split('\n'))
        })

    except Exception as e:
        app.logger.error(f'Error in debug parsing: {str(e)}')
        return jsonify({'error': str(e)}), 500

@app.route('/upload', methods=['POST'])
def upload_file():
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Check file size (max 10MB)
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > 10 * 1024 * 1024:  # 10MB
            return jsonify({'error': 'File size too large. Maximum size is 10MB.'}), 400

        # Extract text based on file type
        filename = file.filename.lower()
        extracted_text = ''

        if filename.endswith('.pdf'):
            extracted_text = extract_text_from_pdf(file)
        elif filename.endswith('.txt'):
            extracted_text = file.read().decode('utf-8')
        else:
            return jsonify({'error': 'Unsupported file type. Please upload a PDF or text file.'}), 400

        if not extracted_text.strip():
            return jsonify({'error': 'No text content found in the file.'}), 400

        return jsonify({'text': extracted_text})

    except Exception as e:
        app.logger.error(f'Error processing file: {str(e)}')
        return jsonify({'error': 'Failed to process file'}), 500

def extract_text_from_pdf(file):
    """Extract text from PDF file using PyPDF2 with better structure preservation"""
    try:
        pdf_reader = PyPDF2.PdfReader(file)
        text = ''

        for page in pdf_reader.pages:
            page_text = page.extract_text()
            text += page_text + '\n'

        # Improve text structure by adding line breaks at logical points
        text = improve_pdf_text_structure(text)
        return text

    except Exception as e:
        raise Exception(f'Failed to extract text from PDF: {str(e)}')

def improve_pdf_text_structure(text):
    """Improve PDF text structure specifically for Alex's resume format"""

    # First, normalize whitespace but preserve some structure
    text = re.sub(r'\s+', ' ', text).strip()

    # Add line breaks before major section headers
    section_patterns = [
        r'(WORK EXPERIENCE)',
        r'(SKILLS & TOOLS)',
        r'(PROJECT HIGHLIGHT)',
        r'(EDUCATION)',
        r'(AWARDS)',
        r'(Languages:)'
    ]

    for pattern in section_patterns:
        text = re.sub(pattern, r'\n\n\1\n', text, flags=re.IGNORECASE)

    # Fix specific patterns in Alex's resume

    # Fix job title patterns - handle cases like "Account Manager | AI automation Gladtobe"
    text = re.sub(r'(Account Manager \|)\s*(AI automation)\s*(Gladtobe)', r'\n\n\1 \2\n\3', text)
    text = re.sub(r'(AI Solutions Consultant)\s*•\s*(Freelancer)', r'\n\n\1\n\2', text)
    text = re.sub(r'(Digital Experience Designer)\s*(ReflectOn)', r'\n\n\1\n\2', text)
    text = re.sub(r'(Project Lead)\s*(Strategy and Innovation)\s*(Bewatec)', r'\n\n\1 \2\n\3', text)
    text = re.sub(r'(Design & Research Innovation Lead)\s*(Bewatec)', r'\n\n\1\n\2', text)

    # Add line breaks before company info with location and dates
    # Pattern: Company • Location Date - Date
    text = re.sub(r'([A-Za-z\s&]+)\s*•\s*([A-Za-z\s,/]+)\s+(\d{2}/\d{4}|\d{4})\s*-\s*(Present|\d{2}/\d{4}|\d{4})',
                  r'\n\1 • \2 \3 - \4\n', text)

    # Add line breaks before bullet points
    text = re.sub(r'\s*•\s*([^•]+)', r'\n• \1', text)

    # Fix education entries
    text = re.sub(r"(Master'?s? in [^•]+)\s*([A-Z][a-z\s]+University)\s*•\s*([A-Za-z\s]+)\s+(\d{2}/\d{4}|\d{4})\s*-\s*(\d{2}/\d{4}|\d{4})",
                  r'\n\1\n\2 • \3 \4 - \5', text)
    text = re.sub(r"(Bachelor'?s? in [^•]+)\s*([A-Z][a-z\s]+University)\s*•\s*([A-Za-z\s]+)\s+(\d{2}/\d{4}|\d{4})\s*-\s*(\d{2}/\d{4}|\d{4})",
                  r'\n\1\n\2 • \3 \4 - \5', text)

    # Clean up multiple consecutive line breaks
    text = re.sub(r'\n{3,}', '\n\n', text)

    # Clean up spaces around line breaks
    text = re.sub(r' *\n *', '\n', text)

    return text.strip()

@app.route('/generate', methods=['POST'])
def generate():
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        job_description = data.get('job_description', '').strip()
        base_resume_content = data.get('base_resume_content', '').strip()
        key_achievements = data.get('key_achievements', '').strip()
        specific_concerns = data.get('specific_concerns', '').strip()

        # Validation
        if not job_description:
            return jsonify({'error': 'Job description is required'}), 400

        if not base_resume_content:
            return jsonify({'error': 'Base resume content is required'}), 400

        # Convert key_achievements to list if it's a string
        if isinstance(key_achievements, str) and key_achievements:
            key_achievements = [achievement.strip() for achievement in key_achievements.split('\n') if achievement.strip()]
        elif not key_achievements:
            key_achievements = []

        result = generate_resume(
            job_description=job_description,
            base_resume_content=base_resume_content,
            key_achievements=key_achievements,
            specific_concerns=specific_concerns
        )

        return jsonify(result)

    except ValueError as e:
        return jsonify({'error': f'Invalid input: {str(e)}'}), 400
    except Exception as e:
        app.logger.error(f'Error generating resume: {str(e)}')
        return jsonify({'error': 'An internal error occurred while generating the resume'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8080)