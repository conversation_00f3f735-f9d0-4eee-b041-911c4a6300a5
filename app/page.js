'use client';
import { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import styles from './page.module.css';

export default function Home() {
  const [jobDescription, setJobDescription] = useState('');
  const [resumeContent, setResumeContent] = useState('');
  const [keyAchievements, setKeyAchievements] = useState('');
  const [specificConcerns, setSpecificConcerns] = useState('');
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [copied, setCopied] = useState(false);
  const [uploadProgress, setUploadProgress] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          job_description: jobDescription,
          base_resume_content: resumeContent,
          key_achievements: keyAchievements,
          specific_concerns: specificConcerns
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setResult(data);
    } catch (error) {
      console.error('Error:', error);
      setError(error.message || 'An error occurred while generating the resume');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToClipboard = async () => {
    if (result?.tailored_resume_markdown) {
      try {
        await navigator.clipboard.writeText(result.tailored_resume_markdown);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error('Failed to copy text: ', err);
      }
    }
  };

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setError(null);
    setUploadProgress('Processing file...');

    try {
      // Validate file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('File size too large. Please upload a file smaller than 10MB.');
      }

      // Validate file type
      const fileName = file.name.toLowerCase();
      if (!fileName.endsWith('.pdf') && !fileName.endsWith('.txt')) {
        throw new Error('Unsupported file type. Please upload a PDF or text file.');
      }

      setUploadProgress('Uploading and processing file...');

      // Create FormData and send to upload endpoint
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process file');
      }

      const result = await response.json();

      if (!result.text || !result.text.trim()) {
        throw new Error('No text content found in the file. Please check your file and try again.');
      }

      setResumeContent(result.text);
      setUploadProgress('File processed successfully!');
      setTimeout(() => setUploadProgress(''), 2000);

    } catch (error) {
      console.error('File upload error:', error);
      setError(error.message);
      setUploadProgress('');
    }
  };

  return (
    <main className={styles.main}>
      <h1 className={styles.title}>Resume Tailor</h1>
      <p className={styles.description}>
        Optimize your resume for specific job applications
      </p>
      
      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.inputGroup}>
          <label htmlFor="jobDescription">Job Description *</label>
          <textarea
            id="jobDescription"
            value={jobDescription}
            onChange={(e) => setJobDescription(e.target.value)}
            placeholder="Paste the full job description here..."
            required
            rows={8}
          />
        </div>

        <div className={styles.inputGroup}>
          <label htmlFor="resumeContent">Your Current Resume *</label>
          <div className={styles.fileUploadSection}>
            <textarea
              id="resumeContent"
              value={resumeContent}
              onChange={(e) => setResumeContent(e.target.value)}
              placeholder="Paste your current resume content here..."
              required
              rows={8}
            />
            <div className={styles.fileUploadOption}>
              <span>Or upload a file:</span>
              <input
                type="file"
                accept=".pdf,.txt"
                onChange={handleFileUpload}
                className={styles.fileInput}
              />
              <span className={styles.fileHint}>
                Supports PDF and text files (max 10MB)
              </span>
            </div>
            {uploadProgress && (
              <div className={styles.uploadProgress}>
                {uploadProgress}
              </div>
            )}
          </div>
        </div>

        <div className={styles.inputGroup}>
          <label htmlFor="keyAchievements">Key Achievements (Optional)</label>
          <textarea
            id="keyAchievements"
            value={keyAchievements}
            onChange={(e) => setKeyAchievements(e.target.value)}
            placeholder="List 1-3 key achievements you want to highlight for this specific job..."
            rows={4}
          />
        </div>

        <div className={styles.inputGroup}>
          <label htmlFor="specificConcerns">Specific Concerns (Optional)</label>
          <textarea
            id="specificConcerns"
            value={specificConcerns}
            onChange={(e) => setSpecificConcerns(e.target.value)}
            placeholder="Any specific parts of your resume you want to improve or aspects of the job you want to emphasize..."
            rows={3}
          />
        </div>

        <button type="submit" className={styles.button} disabled={loading}>
          {loading ? 'Generating Tailored Resume...' : 'Generate Tailored Resume'}
        </button>
      </form>

      {error && (
        <div className={styles.error}>
          <h3>Error</h3>
          <p>{error}</p>
        </div>
      )}
      
      {result && (
        <div className={styles.result}>
          <h2>Your Tailored Resume</h2>

          <div className={styles.scoreCard}>
            <h3>Match Score: {result.match_score}/100</h3>
            <div className={styles.rationale}>
              <ReactMarkdown>{result.match_rationale}</ReactMarkdown>
            </div>
          </div>

          {result.strategic_rationale && (
            <div className={styles.strategicRationale}>
              <h3>Strategic Rationale</h3>
              <ReactMarkdown>{result.strategic_rationale}</ReactMarkdown>
            </div>
          )}

          <div className={styles.resumePreview}>
            <div className={styles.previewHeader}>
              <h3>Resume Preview</h3>
              <div className={styles.actionButtons}>
                <button
                  className={styles.copyButton}
                  onClick={handleCopyToClipboard}
                  title="Copy to clipboard"
                >
                  {copied ? '✓ Copied!' : '📋 Copy'}
                </button>
                <button
                  className={styles.downloadButton}
                  onClick={() => {
                    const blob = new Blob([result.tailored_resume_markdown], {type: 'text/markdown'});
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'tailored_resume.md';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }}
                  title="Download as Markdown"
                >
                  📥 Download MD
                </button>
              </div>
            </div>

            <div className={styles.markdownPreview}>
              <ReactMarkdown>{result.tailored_resume_markdown}</ReactMarkdown>
            </div>

            <div className={styles.rawMarkdown}>
              <h4>Raw Markdown (for copying)</h4>
              <pre className={styles.markdownRaw}>
                {result.tailored_resume_markdown}
              </pre>
            </div>
          </div>
        </div>
      )}
    </main>
  );
}