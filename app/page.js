'use client';
import { useState } from 'react';
import styles from './page.module.css';

export default function Home() {
  const [jobDescription, setJobDescription] = useState('');
  const [resumeContent, setResumeContent] = useState('');
  const [keyAchievements, setKeyAchievements] = useState('');
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          job_description: jobDescription,
          base_resume_content: resumeContent,
          key_achievements: keyAchievements
        }),
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <main className={styles.main}>
      <h1 className={styles.title}>Resume Tailor</h1>
      <p className={styles.description}>
        Optimize your resume for specific job applications
      </p>
      
      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.inputGroup}>
          <label htmlFor="jobDescription">Job Description</label>
          <textarea
            id="jobDescription"
            value={jobDescription}
            onChange={(e) => setJobDescription(e.target.value)}
            placeholder="Paste the full job description here..."
            required
            rows={8}
          />
        </div>
        
        <div className={styles.inputGroup}>
          <label htmlFor="resumeContent">Your Current Resume</label>
          <textarea
            id="resumeContent"
            value={resumeContent}
            onChange={(e) => setResumeContent(e.target.value)}
            placeholder="Paste your current resume content here..."
            required
            rows={8}
          />
        </div>
        
        <div className={styles.inputGroup}>
          <label htmlFor="keyAchievements">Key Achievements (Optional)</label>
          <textarea
            id="keyAchievements"
            value={keyAchievements}
            onChange={(e) => setKeyAchievements(e.target.value)}
            placeholder="List 1-3 key achievements you want to highlight..."
            rows={4}
          />
        </div>
        
        <button type="submit" className={styles.button} disabled={loading}>
          {loading ? 'Generating...' : 'Generate Tailored Resume'}
        </button>
      </form>
      
      {result && (
        <div className={styles.result}>
          <h2>Your Tailored Resume</h2>
          <div className={styles.scoreCard}>
            <h3>Match Score: {result.match_score}/100</h3>
            <p>{result.match_rationale}</p>
          </div>
          <div className={styles.resumePreview}>
            <h3>Resume Preview</h3>
            <div className={styles.markdown}>
              {result.tailored_resume_markdown}
            </div>
            <button 
              className={styles.downloadButton}
              onClick={() => {
                // Add download functionality
                const blob = new Blob([result.tailored_resume_markdown], {type: 'text/markdown'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'tailored_resume.md';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
              }}
            >
              Download Markdown
            </button>
          </div>
        </div>
      )}
    </main>
  );
}