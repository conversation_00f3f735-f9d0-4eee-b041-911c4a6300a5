export async function POST(request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    
    if (!file) {
      return Response.json({ error: 'No file provided' }, { status: 400 });
    }

    // Create a new FormData to send to the Flask backend
    const backendFormData = new FormData();
    backendFormData.append('file', file);

    // Send to Flask backend for processing
    const response = await fetch('http://localhost:8080/upload', {
      method: 'POST',
      body: backendFormData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to process file');
    }

    const result = await response.json();
    return Response.json(result);
    
  } catch (error) {
    console.error('Upload error:', error);
    return Response.json(
      { error: error.message || 'Failed to process file' },
      { status: 500 }
    );
  }
}
