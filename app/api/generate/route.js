export async function POST(request) {
  const data = await request.json();
  
  try {
    // Connect to your Flask backend
    const response = await fetch('http://localhost:8080/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error('Failed to generate resume');
    }
    
    const result = await response.json();
    return Response.json(result);
  } catch (error) {
    console.error('Error:', error);
    return Response.json(
      { error: 'Failed to generate resume' },
      { status: 500 }
    );
  }
}