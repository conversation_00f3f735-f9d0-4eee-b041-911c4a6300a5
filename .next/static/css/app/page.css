/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[7].use[3]!./app/page.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
.page_main__nw1Wk {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.page_title__po7na {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.page_description__lvaOp {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2rem;
}

.page_form__NxPAx {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.page_inputGroup__keP_G {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.page_inputGroup__keP_G label {
  font-weight: 600;
}

.page_inputGroup__keP_G textarea {
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: inherit;
  resize: vertical;
  font-size: 14px;
  line-height: 1.5;
}

.page_fileUploadSection__h651M {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.page_fileUploadOption__lJq5z {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.page_fileInput__3Qiq7 {
  padding: 0.25rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.page_button__52WaL {
  padding: 0.75rem 1.5rem;
  background-color: #0070f3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.page_button__52WaL:hover {
  background-color: #0051a8;
}

.page_button__52WaL:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.page_error__JBrsB {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #fee;
  border: 1px solid #fcc;
  border-radius: 4px;
  color: #c33;
}

.page_result__7WhcO {
  margin-top: 2rem;
  padding: 1.5rem;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  background-color: #fafafa;
}

.page_scoreCard__pEla_ {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 4px;
}

.page_scoreCard__pEla_ h3 {
  color: #2d5a2d;
  margin-bottom: 0.5rem;
}

.page_rationale__E_Uco {
  color: #555;
  font-size: 0.95rem;
}

.page_strategicRationale__zwoxY {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f0f8ff;
  border: 1px solid #b3d9ff;
  border-radius: 4px;
}

.page_strategicRationale__zwoxY h3 {
  color: #1e4a72;
  margin-bottom: 0.5rem;
}

.page_previewHeader__nnSzV {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #ddd;
}

.page_actionButtons__aac_D {
  display: flex;
  gap: 0.5rem;
}

.page_copyButton__UNIa2 {
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.page_copyButton__UNIa2:hover {
  background-color: #0056b3;
}

.page_downloadButton__lvXC9 {
  padding: 0.5rem 1rem;
  background-color: #34a853;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.page_downloadButton__lvXC9:hover {
  background-color: #2d8e47;
}

.page_markdownPreview__w7jd6 {
  padding: 1.5rem;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
}

.page_markdownPreview__w7jd6 h1,
.page_markdownPreview__w7jd6 h2,
.page_markdownPreview__w7jd6 h3 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.page_markdownPreview__w7jd6 h1 {
  font-size: 1.8rem;
  border-bottom: 2px solid #eee;
  padding-bottom: 0.3rem;
}

.page_markdownPreview__w7jd6 h2 {
  font-size: 1.4rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.2rem;
}

.page_markdownPreview__w7jd6 h3 {
  font-size: 1.2rem;
}

.page_markdownPreview__w7jd6 ul {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.page_markdownPreview__w7jd6 li {
  margin-bottom: 0.3rem;
}

.page_markdownPreview__w7jd6 strong {
  font-weight: 600;
}

.page_rawMarkdown__xjVcb {
  margin-top: 1rem;
}

.page_rawMarkdown__xjVcb h4 {
  margin-bottom: 0.5rem;
  color: #666;
  font-size: 1rem;
}

.page_markdownRaw__IVI0h {
  white-space: pre-wrap;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85rem;
  padding: 1rem;
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}
