/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.js */ \"(rsc)/./app/page.js\")), \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\")), \"/Users/<USER>/Documents/augment-projects/Resume custom/app/layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp%2Fglobals.css&server=true!":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp%2Fglobals.css&server=true! ***!
  \***************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp%2Fpage.js&server=true!":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp%2Fpage.js&server=true! ***!
  \***********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.js */ \"(ssr)/./app/page.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhbGV4JTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRlJlc3VtZSUyMGN1c3RvbSUyRmFwcCUyRnBhZ2UuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVzdW1lLXRhaWxvci8/NTJmOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbGV4L0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL1Jlc3VtZSBjdXN0b20vYXBwL3BhZ2UuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp%2Fpage.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhbGV4JTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRlJlc3VtZSUyMGN1c3RvbSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmFwcC1yb3V0ZXIuanMmbW9kdWxlcz0lMkZVc2VycyUyRmFsZXglMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGUmVzdW1lJTIwY3VzdG9tJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGZXJyb3ItYm91bmRhcnkuanMmbW9kdWxlcz0lMkZVc2VycyUyRmFsZXglMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGUmVzdW1lJTIwY3VzdG9tJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGYWxleCUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZSZXN1bWUlMjBjdXN0b20lMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZub3QtZm91bmQtYm91bmRhcnkuanMmbW9kdWxlcz0lMkZVc2VycyUyRmFsZXglMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGUmVzdW1lJTIwY3VzdG9tJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGYWxleCUyRkRvY3VtZW50cyUyRmF1Z21lbnQtcHJvamVjdHMlMkZSZXN1bWUlMjBjdXN0b20lMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWdKO0FBQ2hKLDBPQUFvSjtBQUNwSix3T0FBbUo7QUFDbkosa1BBQXdKO0FBQ3hKLHNRQUFrSztBQUNsSyIsInNvdXJjZXMiOlsid2VicGFjazovL3Jlc3VtZS10YWlsb3IvPzUxYzYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWxleC9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9SZXN1bWUgY3VzdG9tL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsZXgvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvUmVzdW1lIGN1c3RvbS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWxleC9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9SZXN1bWUgY3VzdG9tL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsZXgvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvUmVzdW1lIGN1c3RvbS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsZXgvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvUmVzdW1lIGN1c3RvbS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbGV4L0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL1Jlc3VtZSBjdXN0b20vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9zdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./page.module.css */ \"(ssr)/./app/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Home() {\n    const [jobDescription, setJobDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [resumeContent, setResumeContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [keyAchievements, setKeyAchievements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [specificConcerns, setSpecificConcerns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            const response = await fetch(\"/api/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    job_description: jobDescription,\n                    base_resume_content: resumeContent,\n                    key_achievements: keyAchievements,\n                    specific_concerns: specificConcerns\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const data = await response.json();\n            if (data.error) {\n                throw new Error(data.error);\n            }\n            setResult(data);\n        } catch (error) {\n            console.error(\"Error:\", error);\n            setError(error.message || \"An error occurred while generating the resume\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCopyToClipboard = async ()=>{\n        if (result?.tailored_resume_markdown) {\n            try {\n                await navigator.clipboard.writeText(result.tailored_resume_markdown);\n                setCopied(true);\n                setTimeout(()=>setCopied(false), 2000);\n            } catch (err) {\n                console.error(\"Failed to copy text: \", err);\n            }\n        }\n    };\n    const handleFileUpload = async (e)=>{\n        const file = e.target.files[0];\n        if (!file) return;\n        setError(null);\n        setUploadProgress(\"Processing file...\");\n        try {\n            // Validate file size (10MB max)\n            if (file.size > 10 * 1024 * 1024) {\n                throw new Error(\"File size too large. Please upload a file smaller than 10MB.\");\n            }\n            // Validate file type\n            const fileName = file.name.toLowerCase();\n            if (!fileName.endsWith(\".pdf\") && !fileName.endsWith(\".txt\")) {\n                throw new Error(\"Unsupported file type. Please upload a PDF or text file.\");\n            }\n            setUploadProgress(\"Uploading and processing file...\");\n            // Create FormData and send to upload endpoint\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            const response = await fetch(\"/api/upload\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to process file\");\n            }\n            const result = await response.json();\n            if (!result.text || !result.text.trim()) {\n                throw new Error(\"No text content found in the file. Please check your file and try again.\");\n            }\n            setResumeContent(result.text);\n            setUploadProgress(\"File processed successfully!\");\n            setTimeout(()=>setUploadProgress(\"\"), 2000);\n        } catch (error) {\n            console.error(\"File upload error:\", error);\n            setError(error.message);\n            setUploadProgress(\"\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().main),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().title),\n                children: \"Resume Tailor\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().description),\n                children: \"Optimize your resume for specific job applications\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().form),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().inputGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"jobDescription\",\n                                children: \"Job Description *\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"jobDescription\",\n                                value: jobDescription,\n                                onChange: (e)=>setJobDescription(e.target.value),\n                                placeholder: \"Paste the full job description here...\",\n                                required: true,\n                                rows: 8\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().inputGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"resumeContent\",\n                                children: \"Your Current Resume *\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().fileUploadSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"resumeContent\",\n                                        value: resumeContent,\n                                        onChange: (e)=>setResumeContent(e.target.value),\n                                        placeholder: \"Paste your current resume content here...\",\n                                        required: true,\n                                        rows: 8\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().fileUploadOption),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Or upload a file:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                accept: \".pdf,.txt\",\n                                                onChange: handleFileUpload,\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().fileInput)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().fileHint),\n                                                children: \"Supports PDF and text files (max 10MB)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    uploadProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().uploadProgress),\n                                        children: uploadProgress\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().inputGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"keyAchievements\",\n                                children: \"Key Achievements (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"keyAchievements\",\n                                value: keyAchievements,\n                                onChange: (e)=>setKeyAchievements(e.target.value),\n                                placeholder: \"List 1-3 key achievements you want to highlight for this specific job...\",\n                                rows: 4\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().inputGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"specificConcerns\",\n                                children: \"Specific Concerns (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"specificConcerns\",\n                                value: specificConcerns,\n                                onChange: (e)=>setSpecificConcerns(e.target.value),\n                                placeholder: \"Any specific parts of your resume you want to improve or aspects of the job you want to emphasize...\",\n                                rows: 3\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().button),\n                        disabled: loading,\n                        children: loading ? \"Generating Tailored Resume...\" : \"Generate Tailored Resume\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().error),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().result),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Your Tailored Resume\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().scoreCard),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: [\n                                    \"Match Score: \",\n                                    result.match_score,\n                                    \"/100\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().rationale),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                    children: result.match_rationale\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    result.strategic_rationale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().strategicRationale),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Strategic Rationale\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                children: result.strategic_rationale\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 215,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().resumePreview),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().previewHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Resume Preview\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionButtons),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().copyButton),\n                                                onClick: handleCopyToClipboard,\n                                                title: \"Copy to clipboard\",\n                                                children: copied ? \"✓ Copied!\" : \"\\uD83D\\uDCCB Copy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().downloadButton),\n                                                onClick: ()=>{\n                                                    const blob = new Blob([\n                                                        result.tailored_resume_markdown\n                                                    ], {\n                                                        type: \"text/markdown\"\n                                                    });\n                                                    const url = URL.createObjectURL(blob);\n                                                    const a = document.createElement(\"a\");\n                                                    a.href = url;\n                                                    a.download = \"tailored_resume.md\";\n                                                    document.body.appendChild(a);\n                                                    a.click();\n                                                    document.body.removeChild(a);\n                                                    URL.revokeObjectURL(url);\n                                                },\n                                                title: \"Download as Markdown\",\n                                                children: \"\\uD83D\\uDCE5 Download MD\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().markdownPreview),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                    children: result.tailored_resume_markdown\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().rawMarkdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Raw Markdown (for copying)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().markdownRaw),\n                                        children: result.tailored_resume_markdown\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.js\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"626eba3939be\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZXN1bWUtdGFpbG9yLy4vYXBwL2dsb2JhbHMuY3NzPzgxODEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2MjZlYmEzOTM5YmVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(ssr)/./app/page.module.css":
/*!*****************************!*\
  !*** ./app/page.module.css ***!
  \*****************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"main\": \"page_main__nw1Wk\",\n\t\"title\": \"page_title__po7na\",\n\t\"description\": \"page_description__lvaOp\",\n\t\"form\": \"page_form__NxPAx\",\n\t\"inputGroup\": \"page_inputGroup__keP_G\",\n\t\"fileUploadSection\": \"page_fileUploadSection__h651M\",\n\t\"fileUploadOption\": \"page_fileUploadOption__lJq5z\",\n\t\"fileInput\": \"page_fileInput__3Qiq7\",\n\t\"fileHint\": \"page_fileHint__6NIeM\",\n\t\"uploadProgress\": \"page_uploadProgress__1s8yN\",\n\t\"button\": \"page_button__52WaL\",\n\t\"error\": \"page_error__JBrsB\",\n\t\"result\": \"page_result__7WhcO\",\n\t\"scoreCard\": \"page_scoreCard__pEla_\",\n\t\"rationale\": \"page_rationale__E_Uco\",\n\t\"strategicRationale\": \"page_strategicRationale__zwoxY\",\n\t\"previewHeader\": \"page_previewHeader__nnSzV\",\n\t\"actionButtons\": \"page_actionButtons__aac_D\",\n\t\"copyButton\": \"page_copyButton__UNIa2\",\n\t\"downloadButton\": \"page_downloadButton__lvXC9\",\n\t\"markdownPreview\": \"page_markdownPreview__w7jd6\",\n\t\"rawMarkdown\": \"page_rawMarkdown__xjVcb\",\n\t\"markdownRaw\": \"page_markdownRaw__IVI0h\"\n};\n\nmodule.exports.__checksum = \"edba13f0ba27\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.module.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"Resume Tailor - AI-Powered Resume Optimization\",\n    description: \"Optimize your resume for specific job applications using AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/layout.js\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/layout.js\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFzQjtBQUVmLE1BQU1BLFdBQVc7SUFDdEJDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBRTtJQUM3QyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQU1IOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVzdW1lLXRhaWxvci8uL2FwcC9sYXlvdXQuanM/NjBlOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdSZXN1bWUgVGFpbG9yIC0gQUktUG93ZXJlZCBSZXN1bWUgT3B0aW1pemF0aW9uJyxcbiAgZGVzY3JpcHRpb246ICdPcHRpbWl6ZSB5b3VyIHJlc3VtZSBmb3Igc3BlY2lmaWMgam9iIGFwcGxpY2F0aW9ucyB1c2luZyBBSScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keT57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.js\n");

/***/ }),

/***/ "(rsc)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/micromark-util-symbol","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-from-markdown","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();