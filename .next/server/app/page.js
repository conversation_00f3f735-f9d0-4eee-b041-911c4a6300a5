/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.js */ \"(rsc)/./app/page.js\")), \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\")), \"/Users/<USER>/Documents/augment-projects/Resume custom/app/layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp%2Fglobals.css&server=true!":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp%2Fglobals.css&server=true! ***!
  \***************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp%2Fpage.js&server=true!":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp%2Fpage.js&server=true! ***!
  \***********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.js */ \"(ssr)/./app/page.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhbGV4JTJGRG9jdW1lbnRzJTJGYXVnbWVudC1wcm9qZWN0cyUyRlJlc3VtZSUyMGN1c3RvbSUyRmFwcCUyRnBhZ2UuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVzdW1lLXRhaWxvci8/NTJmOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbGV4L0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL1Jlc3VtZSBjdXN0b20vYXBwL3BhZ2UuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp%2Fpage.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./page.module.css */ \"(ssr)/./app/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Home() {\n    const [jobDescription, setJobDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [resumeContent, setResumeContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [keyAchievements, setKeyAchievements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [specificConcerns, setSpecificConcerns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(null);\n        setResult(null);\n        try {\n            const response = await fetch(\"/api/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    job_description: jobDescription,\n                    base_resume_content: resumeContent,\n                    key_achievements: keyAchievements,\n                    specific_concerns: specificConcerns\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const data = await response.json();\n            if (data.error) {\n                throw new Error(data.error);\n            }\n            setResult(data);\n        } catch (error) {\n            console.error(\"Error:\", error);\n            setError(error.message || \"An error occurred while generating the resume\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCopyToClipboard = async ()=>{\n        if (result?.tailored_resume_markdown) {\n            try {\n                await navigator.clipboard.writeText(result.tailored_resume_markdown);\n                setCopied(true);\n                setTimeout(()=>setCopied(false), 2000);\n            } catch (err) {\n                console.error(\"Failed to copy text: \", err);\n            }\n        }\n    };\n    const handleFileUpload = (e)=>{\n        const file = e.target.files[0];\n        if (file && file.type === \"text/plain\") {\n            const reader = new FileReader();\n            reader.onload = (event)=>{\n                setResumeContent(event.target.result);\n            };\n            reader.readAsText(file);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().main),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().title),\n                children: \"Resume Tailor\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().description),\n                children: \"Optimize your resume for specific job applications\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().form),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().inputGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"jobDescription\",\n                                children: \"Job Description *\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"jobDescription\",\n                                value: jobDescription,\n                                onChange: (e)=>setJobDescription(e.target.value),\n                                placeholder: \"Paste the full job description here...\",\n                                required: true,\n                                rows: 8\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().inputGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"resumeContent\",\n                                children: \"Your Current Resume *\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().fileUploadSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"resumeContent\",\n                                        value: resumeContent,\n                                        onChange: (e)=>setResumeContent(e.target.value),\n                                        placeholder: \"Paste your current resume content here...\",\n                                        required: true,\n                                        rows: 8\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().fileUploadOption),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Or upload a text file:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                accept: \".txt\",\n                                                onChange: handleFileUpload,\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().fileInput)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().inputGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"keyAchievements\",\n                                children: \"Key Achievements (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"keyAchievements\",\n                                value: keyAchievements,\n                                onChange: (e)=>setKeyAchievements(e.target.value),\n                                placeholder: \"List 1-3 key achievements you want to highlight for this specific job...\",\n                                rows: 4\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().inputGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"specificConcerns\",\n                                children: \"Specific Concerns (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"specificConcerns\",\n                                value: specificConcerns,\n                                onChange: (e)=>setSpecificConcerns(e.target.value),\n                                placeholder: \"Any specific parts of your resume you want to improve or aspects of the job you want to emphasize...\",\n                                rows: 3\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().button),\n                        disabled: loading,\n                        children: loading ? \"Generating Tailored Resume...\" : \"Generate Tailored Resume\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().error),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().result),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Your Tailored Resume\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().scoreCard),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: [\n                                    \"Match Score: \",\n                                    result.match_score,\n                                    \"/100\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().rationale),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                    children: result.match_rationale\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    result.strategic_rationale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().strategicRationale),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Strategic Rationale\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                children: result.strategic_rationale\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().resumePreview),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().previewHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Resume Preview\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionButtons),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().copyButton),\n                                                onClick: handleCopyToClipboard,\n                                                title: \"Copy to clipboard\",\n                                                children: copied ? \"✓ Copied!\" : \"\\uD83D\\uDCCB Copy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().downloadButton),\n                                                onClick: ()=>{\n                                                    const blob = new Blob([\n                                                        result.tailored_resume_markdown\n                                                    ], {\n                                                        type: \"text/markdown\"\n                                                    });\n                                                    const url = URL.createObjectURL(blob);\n                                                    const a = document.createElement(\"a\");\n                                                    a.href = url;\n                                                    a.download = \"tailored_resume.md\";\n                                                    document.body.appendChild(a);\n                                                    a.click();\n                                                    document.body.removeChild(a);\n                                                    URL.revokeObjectURL(url);\n                                                },\n                                                title: \"Download as Markdown\",\n                                                children: \"\\uD83D\\uDCE5 Download MD\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().markdownPreview),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                    children: result.tailored_resume_markdown\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().rawMarkdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Raw Markdown (for copying)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_2___default().markdownRaw),\n                                        children: result.tailored_resume_markdown\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.js\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"626eba3939be\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZXN1bWUtdGFpbG9yLy4vYXBwL2dsb2JhbHMuY3NzPzgxODEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2MjZlYmEzOTM5YmVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(ssr)/./app/page.module.css":
/*!*****************************!*\
  !*** ./app/page.module.css ***!
  \*****************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"main\": \"page_main__nw1Wk\",\n\t\"title\": \"page_title__po7na\",\n\t\"description\": \"page_description__lvaOp\",\n\t\"form\": \"page_form__NxPAx\",\n\t\"inputGroup\": \"page_inputGroup__keP_G\",\n\t\"fileUploadSection\": \"page_fileUploadSection__h651M\",\n\t\"fileUploadOption\": \"page_fileUploadOption__lJq5z\",\n\t\"fileInput\": \"page_fileInput__3Qiq7\",\n\t\"button\": \"page_button__52WaL\",\n\t\"error\": \"page_error__JBrsB\",\n\t\"result\": \"page_result__7WhcO\",\n\t\"scoreCard\": \"page_scoreCard__pEla_\",\n\t\"rationale\": \"page_rationale__E_Uco\",\n\t\"strategicRationale\": \"page_strategicRationale__zwoxY\",\n\t\"previewHeader\": \"page_previewHeader__nnSzV\",\n\t\"actionButtons\": \"page_actionButtons__aac_D\",\n\t\"copyButton\": \"page_copyButton__UNIa2\",\n\t\"downloadButton\": \"page_downloadButton__lvXC9\",\n\t\"markdownPreview\": \"page_markdownPreview__w7jd6\",\n\t\"rawMarkdown\": \"page_rawMarkdown__xjVcb\",\n\t\"markdownRaw\": \"page_markdownRaw__IVI0h\"\n};\n\nmodule.exports.__checksum = \"c82bb02bae91\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZXN1bWUtdGFpbG9yLy4vYXBwL3BhZ2UubW9kdWxlLmNzcz80ZjI4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIm1haW5cIjogXCJwYWdlX21haW5fX253MVdrXCIsXG5cdFwidGl0bGVcIjogXCJwYWdlX3RpdGxlX19wbzduYVwiLFxuXHRcImRlc2NyaXB0aW9uXCI6IFwicGFnZV9kZXNjcmlwdGlvbl9fbHZhT3BcIixcblx0XCJmb3JtXCI6IFwicGFnZV9mb3JtX19OeFBBeFwiLFxuXHRcImlucHV0R3JvdXBcIjogXCJwYWdlX2lucHV0R3JvdXBfX2tlUF9HXCIsXG5cdFwiZmlsZVVwbG9hZFNlY3Rpb25cIjogXCJwYWdlX2ZpbGVVcGxvYWRTZWN0aW9uX19oNjUxTVwiLFxuXHRcImZpbGVVcGxvYWRPcHRpb25cIjogXCJwYWdlX2ZpbGVVcGxvYWRPcHRpb25fX2xKcTV6XCIsXG5cdFwiZmlsZUlucHV0XCI6IFwicGFnZV9maWxlSW5wdXRfXzNRaXE3XCIsXG5cdFwiYnV0dG9uXCI6IFwicGFnZV9idXR0b25fXzUyV2FMXCIsXG5cdFwiZXJyb3JcIjogXCJwYWdlX2Vycm9yX19KQnJzQlwiLFxuXHRcInJlc3VsdFwiOiBcInBhZ2VfcmVzdWx0X183V2hjT1wiLFxuXHRcInNjb3JlQ2FyZFwiOiBcInBhZ2Vfc2NvcmVDYXJkX19wRWxhX1wiLFxuXHRcInJhdGlvbmFsZVwiOiBcInBhZ2VfcmF0aW9uYWxlX19FX1Vjb1wiLFxuXHRcInN0cmF0ZWdpY1JhdGlvbmFsZVwiOiBcInBhZ2Vfc3RyYXRlZ2ljUmF0aW9uYWxlX196d294WVwiLFxuXHRcInByZXZpZXdIZWFkZXJcIjogXCJwYWdlX3ByZXZpZXdIZWFkZXJfX25uU3pWXCIsXG5cdFwiYWN0aW9uQnV0dG9uc1wiOiBcInBhZ2VfYWN0aW9uQnV0dG9uc19fYWFjX0RcIixcblx0XCJjb3B5QnV0dG9uXCI6IFwicGFnZV9jb3B5QnV0dG9uX19VTklhMlwiLFxuXHRcImRvd25sb2FkQnV0dG9uXCI6IFwicGFnZV9kb3dubG9hZEJ1dHRvbl9fbHZYQzlcIixcblx0XCJtYXJrZG93blByZXZpZXdcIjogXCJwYWdlX21hcmtkb3duUHJldmlld19fdzdqZDZcIixcblx0XCJyYXdNYXJrZG93blwiOiBcInBhZ2VfcmF3TWFya2Rvd25fX3hqVmNiXCIsXG5cdFwibWFya2Rvd25SYXdcIjogXCJwYWdlX21hcmtkb3duUmF3X19JVkkwaFwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJjODJiYjAyYmFlOTFcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/page.module.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"Resume Tailor - AI-Powered Resume Optimization\",\n    description: \"Optimize your resume for specific job applications using AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/layout.js\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Resume custom/app/layout.js\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFzQjtBQUVmLE1BQU1BLFdBQVc7SUFDdEJDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBRTtJQUM3QyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7c0JBQU1IOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVzdW1lLXRhaWxvci8uL2FwcC9sYXlvdXQuanM/NjBlOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdSZXN1bWUgVGFpbG9yIC0gQUktUG93ZXJlZCBSZXN1bWUgT3B0aW1pemF0aW9uJyxcbiAgZGVzY3JpcHRpb246ICdPcHRpbWl6ZSB5b3VyIHJlc3VtZSBmb3Igc3BlY2lmaWMgam9iIGFwcGxpY2F0aW9ucyB1c2luZyBBSScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keT57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.js\n");

/***/ }),

/***/ "(rsc)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/@swc","vendor-chunks/micromark-util-symbol","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-from-markdown","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falex%2FDocuments%2Faugment-projects%2FResume%20custom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();