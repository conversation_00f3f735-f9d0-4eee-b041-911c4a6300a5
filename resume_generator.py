import re

def generate_resume(
    job_description: str,
    base_resume_content: str,
    layout_preference: dict = None,
    key_achievements: list = None,
    specific_concerns: str = None
) -> dict:
    """
    Generates a tailored resume in Markdown format based on the provided inputs.

    Args:
        job_description (str): Full job advertisement text.
        base_resume_content (str): Plain text content of the user's current resume.
        layout_preference (dict): Dictionary describing the preferred resume layout (e.g., section order, bolding).
                                  Currently, this will be a simplified representation.
        key_achievements (list): List of 1-3 specific achievements/projects to highlight.
        specific_concerns (str): User's specific concerns or areas for improvement.

    Returns:
        dict: A dictionary containing:
            - 'tailored_resume_markdown': The generated resume in Markdown format.
            - 'match_score': A numerical score indicating alignment with the job description.
            - 'strategic_rationale': Explanation of key strategic choices.
    """

    # --- 1. Parse Inputs ---
    # For now, we'll treat base_resume_content as a simple string.
    # In a real system, this would involve more sophisticated parsing (e.g., from PDF, DOCX).

    # --- 2. Core AI Strategy & Guiding Principles (Simplified Implementation) ---

    # 2.1. Strategic Positioning & Summary Generation
    # This is a highly simplified version. A real AI would use NLP models.
    summary_keywords = extract_keywords(job_description, num_keywords=10)
    summary_base = "AI Consultant | Digital Product Strategist | Account Manager"
    tailored_summary = f"""
    {summary_base}

    Highly accomplished professional with a proven track record in leveraging AI and digital strategies to drive business growth. 
    Expert in {', '.join(summary_keywords[:3])}. Seeking to apply expertise in {job_description.split('.')[0].lower()} 
    to contribute to [Target Company Name, inferred from JD] success.
    """

    # 2.2. Contextual Proof & Quantifiable Impact (CAR + Why)
    # This is a placeholder. Actual implementation would involve identifying action verbs, numbers, and results.
    # For now, we'll just reformat existing bullet points if they exist.
    processed_experience = process_experience_section(base_resume_content, job_description, key_achievements)

    # 2.3. Authentic Human Voice & 2.4. Cultural & Linguistic Alignment
    # These are largely handled by careful prompt engineering in a real LLM scenario.
    # For this script, we'll aim for a professional, direct tone.

    # 2.5. Signature Project/Achievement Highlight
    signature_highlight = ""
    if key_achievements:
        signature_highlight = f"**Signature Achievement:** {key_achievements[0]}. " \
                              f"This directly aligns with the {job_description.split('.')[0].lower()} requirements."

    # --- 3. Output Requirements & Format ---

    # 3.1. Format: Markdown
    # 3.2. Resume Structure (Adhering to User's Provided Layout)
    # This is a hardcoded structure based on the PRD's example.

    contact_info = "Name, Pufendorfstrasse 7A, Berlin • +4915774654068 • <EMAIL> • linkedin.com/in/alexguyenne"

    # Placeholder for sections. In a real system, these would be dynamically populated.
    skills_section = """## SKILLS
*   **Technical:** Python, SQL, Data Analysis, Machine Learning
*   **Business:** Product Management, Strategy, Consulting, Project Management
"""

    languages_section = """## LANGUAGES
*   English (Native)
*   German (Fluent)
"""

    education_section = """## EDUCATION
*   **Master of Science in AI**, University of Berlin, 2023
*   **Bachelor of Business Administration**, Berlin School of Economics and Law, 2021
"""

    tailored_resume_markdown = f"""
{contact_info}

{tailored_summary}

## WORK EXPERIENCE
{processed_experience}

{skills_section}

{languages_section}

{education_section}
"""

    # --- 3.3. Content & Style ---
    # Ensured by the generation logic (simplified here).

    # --- 3.4. Deliverables ---
    # 1. The Tailored Resume (already generated above)

    # 2. Match Score & Rationale (Simplified)
    match_score = calculate_match_score(job_description, tailored_resume_markdown)
    rationale_improvements = "Key improvements include tailoring the summary to job description keywords and highlighting relevant experience."
    rationale_further_personalization = "Further personalization could involve adding specific quantifiable metrics if available."
    match_rationale = f"""
Match Score: {match_score}/100

Rationale:
- {rationale_improvements}
- {rationale_further_personalization}
"""

    # 3. Strategic Rationale (Simplified)
    strategic_rationale_text = f"""
Strategic Rationale:
- The summary was rephrased to align with the job description's emphasis on {summary_keywords[0]} and {summary_keywords[1]}.
- A signature achievement related to {key_achievements[0] if key_achievements else 'driving impact'} was highlighted to demonstrate direct relevance.
- Experience bullet points were re-evaluated for stronger action verbs and potential for quantifiable results.
"""

    return {
        'tailored_resume_markdown': tailored_resume_markdown,
        'match_score': match_score,
        'match_rationale': match_rationale,
        'strategic_rationale': strategic_rationale_text
    }

def extract_keywords(text: str, num_keywords: int = 5) -> list:
    """
    A very basic keyword extraction using regex. In a real system, this would be NLP-driven.
    """
    words = re.findall(r'\b\w+\b', text.lower())
    # Filter out common words and return unique ones
    common_words = {'a', 'an', 'the', 'and', 'or', 'in', 'on', 'at', 'for', 'with', 'to', 'of', 'is', 'are', 'be', 'by'}
    keywords = sorted(list(set([word for word in words if word not in common_words])))[:num_keywords]
    return keywords

def process_experience_section(base_content: str, job_description: str, key_achievements: list) -> str:
    """
    A highly simplified function to process the experience section.
    In a real scenario, this would involve parsing, re-writing, and optimizing bullet points.
    For now, it just returns a placeholder or a slightly modified version of existing content.
    """
    # This is a mock-up. Assume base_content has a WORK EXPERIENCE section.
    # We'll just return a sample tailored experience.

    sample_experience = """
### Gladtobe, Berlin, Germany
**AI Consultant** | 01/2024 - Present
*   Led the development of an AI-powered recommendation engine, increasing user engagement by 25%.
*   Collaborated with cross-functional teams to integrate machine learning solutions into existing products.
*   {key_achievements[0] if key_achievements else 'Successfully managed client relationships and project deliverables.'}

### Previous Company, City, Country
**Digital Product Manager** | 06/2021 - 12/2023
*   Managed the full product lifecycle for a SaaS platform, resulting in a 15% increase in recurring revenue.
*   Implemented data-driven strategies to optimize user acquisition funnels.
"""
    return sample_experience

def calculate_match_score(job_description: str, resume_content: str) -> int:
    """
    A very basic match score calculation based on keyword overlap.
    """
    jd_keywords = set(extract_keywords(job_description, num_keywords=20))
    resume_keywords = set(extract_keywords(resume_content, num_keywords=50))

    common_keywords = jd_keywords.intersection(resume_keywords)
    score = int((len(common_keywords) / len(jd_keywords)) * 100) if len(jd_keywords) > 0 else 0
    return min(score, 100) # Cap at 100


if __name__ == "__main__":
    # Example Usage:
    sample_job_description = """
    We are seeking a highly motivated AI Consultant with strong experience in machine learning, 
    data analysis, and strategic consulting. The ideal candidate will have a proven track record 
    of delivering impactful AI solutions and working with cross-functional teams. 
    Experience in product management and client-facing roles is a plus.
    """

    sample_base_resume_content = """
    Name: Alex Guyenne
    Summary: AI Consultant | Digital Product Strategist | Account Manager. I help companies automate workflows.
    Experience: Gladtobe (AI Consultant), Previous Company (Digital Product Manager)
    Skills: Python, SQL, Product Management
    Education: Master of Science in AI
    """

    sample_key_achievements = [
        "Led development of an AI assistant for a German telecom, automating content creation and reducing delivery time by 60%."
    ]

    result = generate_resume(
        job_description=sample_job_description,
        base_resume_content=sample_base_resume_content,
        key_achievements=sample_key_achievements
    )

    print("--- Tailored Resume (Markdown) ---")
    print(result['tailored_resume_markdown'])
    print("\n--- Match Score & Rationale ---")
    print(result['match_rationale'])
    print("\n--- Strategic Rationale ---")
    print(result['strategic_rationale'])