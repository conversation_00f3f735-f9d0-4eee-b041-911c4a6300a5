import re

def generate_resume(
    job_description: str,
    base_resume_content: str,
    layout_preference: dict = None,
    key_achievements: list = None,
    specific_concerns: str = None
) -> dict:
    """
    Generates a tailored resume in Markdown format based on the provided inputs.

    Args:
        job_description (str): Full job advertisement text.
        base_resume_content (str): Plain text content of the user's current resume.
        layout_preference (dict): Dictionary describing the preferred resume layout (e.g., section order, bolding).
                                  Currently, this will be a simplified representation.
        key_achievements (list): List of 1-3 specific achievements/projects to highlight.
        specific_concerns (str): User's specific concerns or areas for improvement.

    Returns:
        dict: A dictionary containing:
            - 'tailored_resume_markdown': The generated resume in Markdown format.
            - 'match_score': A numerical score indicating alignment with the job description.
            - 'strategic_rationale': Explanation of key strategic choices.
    """

    # --- 1. Parse Inputs ---
    parsed_resume = parse_resume_content(base_resume_content)

    # --- 2. Core AI Strategy & Guiding Principles (Enhanced Implementation) ---

    # 2.1. Strategic Positioning & Summary Generation
    summary_keywords = extract_keywords(job_description, num_keywords=10)

    # Use parsed name if available, otherwise use default
    name = parsed_resume.get('name', 'Professional Name')
    contact_info = parsed_resume.get('contact', 'Contact Information')

    # Create tailored summary based on job requirements
    role_keywords = [kw for kw in summary_keywords if kw in ['consultant', 'manager', 'engineer', 'analyst', 'developer', 'strategist']]
    primary_role = role_keywords[0].title() if role_keywords else 'Professional'

    # Extract company name from job description if possible
    company_name = extract_company_name(job_description)

    tailored_summary = f"""**{primary_role} | Digital Innovation Specialist**

Accomplished professional with proven expertise in {', '.join(summary_keywords[:3])}. Demonstrated track record of delivering impactful solutions that drive business growth and operational excellence. {f'Seeking to contribute to {company_name}' if company_name else 'Seeking to apply expertise'} by leveraging deep technical knowledge and strategic thinking to achieve organizational objectives."""

    # 2.2. Contextual Proof & Quantifiable Impact (CAR + Why)
    processed_experience = process_experience_section(base_resume_content, job_description, key_achievements)

    # 2.3. Authentic Human Voice & 2.4. Cultural & Linguistic Alignment
    # These are largely handled by careful prompt engineering in a real LLM scenario.
    # For this script, we'll aim for a professional, direct tone.

    # 2.5. Signature Project/Achievement Highlight
    signature_highlight = ""
    if key_achievements:
        signature_highlight = f"**Signature Achievement:** {key_achievements[0]}. " \
                              f"This directly aligns with the {job_description.split('.')[0].lower()} requirements."

    # --- 3. Output Requirements & Format ---

    # 3.1. Format: Markdown
    # 3.2. Resume Structure (Enhanced with parsed content)

    # Use parsed contact info or create from available data
    if contact_info and contact_info != 'Contact Information':
        header_info = f"{name}\n{contact_info}"
    else:
        header_info = f"{name}\nProfessional Contact Information"

    # Create tailored skills section
    skills_section = create_skills_section(parsed_resume.get('skills', []), summary_keywords)

    # Create languages section from parsed content or use default
    languages_section = create_languages_section(parsed_resume.get('languages', []))

    # Create education section from parsed content or use default
    education_section = create_education_section(parsed_resume.get('education', []))

    tailored_resume_markdown = f"""{header_info}

{tailored_summary}

## WORK EXPERIENCE
{processed_experience}

{skills_section}

{languages_section}

{education_section}
"""

    # --- 3.3. Content & Style ---
    # Ensured by the generation logic (simplified here).

    # --- 3.4. Deliverables ---
    # 1. The Tailored Resume (already generated above)

    # 2. Match Score & Rationale (Simplified)
    match_score = calculate_match_score(job_description, tailored_resume_markdown)
    rationale_improvements = "Key improvements include tailoring the summary to job description keywords and highlighting relevant experience."
    rationale_further_personalization = "Further personalization could involve adding specific quantifiable metrics if available."
    match_rationale = f"""
Match Score: {match_score}/100

Rationale:
- {rationale_improvements}
- {rationale_further_personalization}
"""

    # 3. Strategic Rationale (Simplified)
    strategic_rationale_text = f"""
Strategic Rationale:
- The summary was rephrased to align with the job description's emphasis on {summary_keywords[0]} and {summary_keywords[1]}.
- A signature achievement related to {key_achievements[0] if key_achievements else 'driving impact'} was highlighted to demonstrate direct relevance.
- Experience bullet points were re-evaluated for stronger action verbs and potential for quantifiable results.
"""

    return {
        'tailored_resume_markdown': tailored_resume_markdown,
        'match_score': match_score,
        'match_rationale': match_rationale,
        'strategic_rationale': strategic_rationale_text
    }

def extract_keywords(text: str, num_keywords: int = 5) -> list:
    """
    Enhanced keyword extraction that prioritizes technical terms and important concepts.
    """
    words = re.findall(r'\b\w+\b', text.lower())

    # Expanded common words to filter out
    common_words = {
        'a', 'an', 'the', 'and', 'or', 'in', 'on', 'at', 'for', 'with', 'to', 'of', 'is', 'are', 'be', 'by',
        'this', 'that', 'these', 'those', 'will', 'would', 'could', 'should', 'can', 'may', 'might',
        'have', 'has', 'had', 'do', 'does', 'did', 'get', 'got', 'make', 'made', 'take', 'took',
        'go', 'went', 'come', 'came', 'see', 'saw', 'know', 'knew', 'think', 'thought', 'say', 'said',
        'work', 'working', 'worked', 'job', 'position', 'role', 'company', 'team', 'project', 'projects'
    }

    # Technical and business terms that should be prioritized
    priority_terms = {
        'python', 'javascript', 'react', 'node', 'sql', 'database', 'api', 'cloud', 'aws', 'azure',
        'machine', 'learning', 'ai', 'artificial', 'intelligence', 'data', 'analytics', 'science',
        'management', 'strategy', 'consulting', 'product', 'digital', 'transformation', 'agile',
        'scrum', 'leadership', 'optimization', 'automation', 'integration', 'development',
        'engineering', 'architecture', 'design', 'analysis', 'implementation', 'deployment'
    }

    # Filter and score words
    word_scores = {}
    for word in words:
        if len(word) > 2 and word not in common_words:
            score = 1
            if word in priority_terms:
                score = 3
            elif word.endswith(('ing', 'ed', 'er', 'ly')):
                score = 0.5
            word_scores[word] = word_scores.get(word, 0) + score

    # Sort by score and frequency, return top keywords
    sorted_words = sorted(word_scores.items(), key=lambda x: x[1], reverse=True)
    keywords = [word for word, score in sorted_words[:num_keywords]]
    return keywords

def process_experience_section(base_content: str, job_description: str, key_achievements: list) -> str:
    """
    Enhanced function to process and tailor the experience section based on job requirements.
    """
    # Extract job keywords for tailoring
    jd_keywords = extract_keywords(job_description, num_keywords=15)

    # Try to parse existing experience from base content
    parsed_experience = parse_resume_content(base_content)

    if parsed_experience.get('experience'):
        # Use parsed experience and enhance it
        return enhance_experience_section(parsed_experience['experience'], jd_keywords, key_achievements)
    else:
        # Fallback to template with user's information if available
        return create_template_experience(base_content, jd_keywords, key_achievements)

def parse_resume_content(content: str) -> dict:
    """
    Parse resume content to extract structured information.
    """
    parsed = {
        'name': '',
        'contact': '',
        'summary': '',
        'experience': [],
        'skills': [],
        'education': [],
        'languages': []
    }

    lines = content.split('\n')
    current_section = None

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Detect sections
        line_lower = line.lower()
        if any(keyword in line_lower for keyword in ['experience', 'work', 'employment']):
            current_section = 'experience'
        elif any(keyword in line_lower for keyword in ['skill', 'technical', 'competenc']):
            current_section = 'skills'
        elif any(keyword in line_lower for keyword in ['education', 'academic', 'degree']):
            current_section = 'education'
        elif any(keyword in line_lower for keyword in ['language', 'linguistic']):
            current_section = 'languages'
        elif any(keyword in line_lower for keyword in ['summary', 'profile', 'objective']):
            current_section = 'summary'
        elif not current_section and '@' in line:
            parsed['contact'] = line
        elif not current_section and not parsed['name']:
            parsed['name'] = line
        else:
            # Add content to current section
            if current_section == 'experience':
                parsed['experience'].append(line)
            elif current_section == 'skills':
                parsed['skills'].append(line)
            elif current_section == 'education':
                parsed['education'].append(line)
            elif current_section == 'languages':
                parsed['languages'].append(line)
            elif current_section == 'summary':
                parsed['summary'] += ' ' + line

    return parsed

def enhance_experience_section(experience_lines: list, jd_keywords: list, key_achievements: list) -> str:
    """
    Enhance existing experience with job-relevant keywords and achievements.
    """
    enhanced_experience = ""

    # Group experience lines into jobs
    current_job = []
    jobs = []

    for line in experience_lines:
        if line and (any(char.isdigit() for char in line) and ('20' in line or '19' in line)) or \
           (line.isupper() or line.count(',') >= 1):
            # Likely a new job header
            if current_job:
                jobs.append(current_job)
            current_job = [line]
        else:
            current_job.append(line)

    if current_job:
        jobs.append(current_job)

    # Enhance each job
    for i, job in enumerate(jobs):
        if not job:
            continue

        header = job[0] if job else "Previous Role"
        bullets = job[1:] if len(job) > 1 else []

        enhanced_experience += f"\n### {header}\n"

        # Add key achievement if this is the first job and we have achievements
        if i == 0 and key_achievements:
            enhanced_experience += f"*   **Key Achievement:** {key_achievements[0]}\n"

        # Enhance existing bullets or create new ones
        if bullets:
            for bullet in bullets:
                if bullet.strip():
                    enhanced_bullet = enhance_bullet_point(bullet, jd_keywords)
                    enhanced_experience += f"*   {enhanced_bullet}\n"
        else:
            # Create template bullets based on job keywords
            enhanced_experience += create_template_bullets(jd_keywords)

        enhanced_experience += "\n"

    return enhanced_experience

def enhance_bullet_point(bullet: str, jd_keywords: list) -> str:
    """
    Enhance a bullet point to include relevant keywords and stronger action verbs.
    """
    # Strong action verbs
    action_verbs = [
        'Led', 'Developed', 'Implemented', 'Optimized', 'Managed', 'Created', 'Designed',
        'Delivered', 'Achieved', 'Improved', 'Increased', 'Reduced', 'Streamlined',
        'Collaborated', 'Coordinated', 'Executed', 'Established', 'Transformed'
    ]

    # Clean up the bullet
    bullet = bullet.strip('*- ').strip()

    # If bullet doesn't start with a strong action verb, try to improve it
    if not any(bullet.startswith(verb) for verb in action_verbs):
        # Try to identify the action and replace with stronger verb
        if 'worked' in bullet.lower():
            bullet = bullet.replace('worked', 'collaborated', 1)
        elif 'did' in bullet.lower():
            bullet = bullet.replace('did', 'executed', 1)
        elif 'made' in bullet.lower():
            bullet = bullet.replace('made', 'created', 1)

    # Ensure it starts with capital letter
    if bullet:
        bullet = bullet[0].upper() + bullet[1:]

    return bullet

def create_template_bullets(jd_keywords: list) -> str:
    """
    Create template bullet points based on job keywords.
    """
    bullets = ""

    # Create bullets that incorporate job keywords
    if any(keyword in jd_keywords for keyword in ['data', 'analysis', 'analytics']):
        bullets += "*   Analyzed complex datasets to drive strategic decision-making and business insights\n"

    if any(keyword in jd_keywords for keyword in ['team', 'leadership', 'manage']):
        bullets += "*   Led cross-functional teams to deliver high-impact projects on time and within budget\n"

    if any(keyword in jd_keywords for keyword in ['product', 'development', 'software']):
        bullets += "*   Developed and launched innovative solutions that improved operational efficiency\n"

    if any(keyword in jd_keywords for keyword in ['client', 'customer', 'stakeholder']):
        bullets += "*   Collaborated with key stakeholders to understand requirements and deliver tailored solutions\n"

    # Default bullet if no specific keywords match
    if not bullets:
        bullets = "*   Successfully delivered key initiatives that contributed to organizational goals\n"

    return bullets

def create_template_experience(base_content: str, jd_keywords: list, key_achievements: list) -> str:
    """
    Create a template experience section when parsing fails.
    """
    # Try to extract any company or role information from base content
    lines = base_content.split('\n')
    company_info = []

    for line in lines:
        if any(keyword in line.lower() for keyword in ['company', 'corp', 'inc', 'ltd', 'gmbh', 'consultant', 'manager', 'engineer', 'analyst']):
            company_info.append(line.strip())

    experience = ""

    if company_info:
        for info in company_info[:2]:  # Use up to 2 pieces of company info
            experience += f"\n### {info}\n"
            if key_achievements:
                experience += f"*   **Key Achievement:** {key_achievements.pop(0)}\n"
            experience += create_template_bullets(jd_keywords)
            experience += "\n"
    else:
        # Complete fallback template
        experience = f"""
### Current/Recent Position
**Professional Role** | Recent Period
{f'*   **Key Achievement:** {key_achievements[0]}' if key_achievements else ''}
{create_template_bullets(jd_keywords)}

### Previous Position
**Previous Role** | Previous Period
*   Delivered impactful results in fast-paced environment
*   Collaborated with diverse teams to achieve organizational objectives
"""

    return experience

def extract_company_name(job_description: str) -> str:
    """
    Try to extract company name from job description.
    """
    lines = job_description.split('\n')
    for line in lines[:5]:  # Check first few lines
        line = line.strip()
        if line and not line.lower().startswith(('we are', 'job', 'position', 'role', 'about')):
            # Simple heuristic: if it's a short line early in the description, might be company name
            if len(line.split()) <= 4 and len(line) > 2:
                return line
    return ""

def create_skills_section(parsed_skills: list, jd_keywords: list) -> str:
    """
    Create a tailored skills section based on parsed skills and job requirements.
    """
    # Default technical and business skills
    default_technical = ['Python', 'SQL', 'Data Analysis', 'Project Management']
    default_business = ['Strategic Planning', 'Stakeholder Management', 'Process Optimization']

    # Extract skills from parsed content
    technical_skills = []
    business_skills = []

    for skill_line in parsed_skills:
        skill_line_lower = skill_line.lower()
        if any(tech in skill_line_lower for tech in ['python', 'sql', 'javascript', 'react', 'node', 'aws', 'cloud', 'api', 'database']):
            technical_skills.extend([s.strip() for s in skill_line.split(',') if s.strip()])
        elif any(biz in skill_line_lower for biz in ['management', 'strategy', 'consulting', 'leadership', 'analysis']):
            business_skills.extend([s.strip() for s in skill_line.split(',') if s.strip()])
        else:
            # Add to technical by default
            technical_skills.extend([s.strip() for s in skill_line.split(',') if s.strip()])

    # Use defaults if no skills parsed
    if not technical_skills:
        technical_skills = default_technical
    if not business_skills:
        business_skills = default_business

    # Add job-relevant skills
    for keyword in jd_keywords[:5]:
        if keyword.lower() not in [s.lower() for s in technical_skills + business_skills]:
            if keyword.lower() in ['python', 'sql', 'javascript', 'react', 'aws', 'cloud', 'api', 'machine', 'learning', 'ai']:
                technical_skills.append(keyword.title())
            else:
                business_skills.append(keyword.title())

    skills_section = "## SKILLS\n"
    if technical_skills:
        skills_section += f"*   **Technical:** {', '.join(technical_skills[:8])}\n"
    if business_skills:
        skills_section += f"*   **Business:** {', '.join(business_skills[:8])}\n"

    return skills_section

def create_languages_section(parsed_languages: list) -> str:
    """
    Create languages section from parsed content or use defaults.
    """
    if parsed_languages:
        languages_section = "## LANGUAGES\n"
        for lang_line in parsed_languages:
            if lang_line.strip():
                languages_section += f"*   {lang_line.strip()}\n"
        return languages_section
    else:
        return """## LANGUAGES
*   English (Professional)
*   Additional languages as applicable
"""

def create_education_section(parsed_education: list) -> str:
    """
    Create education section from parsed content or use defaults.
    """
    if parsed_education:
        education_section = "## EDUCATION\n"
        for edu_line in parsed_education:
            if edu_line.strip():
                education_section += f"*   {edu_line.strip()}\n"
        return education_section
    else:
        return """## EDUCATION
*   **Relevant Degree**, Institution, Year
*   **Additional Qualifications** as applicable
"""

def calculate_match_score(job_description: str, resume_content: str) -> int:
    """
    Enhanced match score calculation based on keyword overlap and relevance.
    """
    jd_keywords = set(extract_keywords(job_description, num_keywords=20))
    resume_keywords = set(extract_keywords(resume_content, num_keywords=50))

    common_keywords = jd_keywords.intersection(resume_keywords)

    # Base score from keyword overlap
    base_score = int((len(common_keywords) / len(jd_keywords)) * 100) if len(jd_keywords) > 0 else 0

    # Bonus points for high-value keywords
    high_value_keywords = {'python', 'sql', 'machine', 'learning', 'ai', 'management', 'strategy', 'consulting', 'leadership'}
    high_value_matches = common_keywords.intersection(high_value_keywords)
    bonus_score = len(high_value_matches) * 5

    final_score = min(base_score + bonus_score, 100)
    return final_score


if __name__ == "__main__":
    # Example Usage:
    sample_job_description = """
    We are seeking a highly motivated AI Consultant with strong experience in machine learning, 
    data analysis, and strategic consulting. The ideal candidate will have a proven track record 
    of delivering impactful AI solutions and working with cross-functional teams. 
    Experience in product management and client-facing roles is a plus.
    """

    sample_base_resume_content = """
    Name: Alex Guyenne
    Summary: AI Consultant | Digital Product Strategist | Account Manager. I help companies automate workflows.
    Experience: Gladtobe (AI Consultant), Previous Company (Digital Product Manager)
    Skills: Python, SQL, Product Management
    Education: Master of Science in AI
    """

    sample_key_achievements = [
        "Led development of an AI assistant for a German telecom, automating content creation and reducing delivery time by 60%."
    ]

    result = generate_resume(
        job_description=sample_job_description,
        base_resume_content=sample_base_resume_content,
        key_achievements=sample_key_achievements
    )

    print("--- Tailored Resume (Markdown) ---")
    print(result['tailored_resume_markdown'])
    print("\n--- Match Score & Rationale ---")
    print(result['match_rationale'])
    print("\n--- Strategic Rationale ---")
    print(result['strategic_rationale'])