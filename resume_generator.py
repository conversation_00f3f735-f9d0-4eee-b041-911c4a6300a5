import re

def generate_resume(
    job_description: str,
    base_resume_content: str,
    layout_preference: dict = None,
    key_achievements: list = None,
    specific_concerns: str = None
) -> dict:
    """
    Generates a tailored resume in Markdown format based on the provided inputs.

    Args:
        job_description (str): Full job advertisement text.
        base_resume_content (str): Plain text content of the user's current resume.
        layout_preference (dict): Dictionary describing the preferred resume layout (e.g., section order, bolding).
                                  Currently, this will be a simplified representation.
        key_achievements (list): List of 1-3 specific achievements/projects to highlight.
        specific_concerns (str): User's specific concerns or areas for improvement.

    Returns:
        dict: A dictionary containing:
            - 'tailored_resume_markdown': The generated resume in Markdown format.
            - 'match_score': A numerical score indicating alignment with the job description.
            - 'strategic_rationale': Explanation of key strategic choices.
    """

    # --- 1. Parse Inputs ---
    parsed_resume = parse_resume_content(base_resume_content)

    # --- 2. Core AI Strategy & Guiding Principles (Enhanced Implementation) ---

    # 2.1. Strategic Positioning & Summary Generation
    summary_keywords = extract_keywords(job_description, num_keywords=10)

    # Use parsed information
    name = parsed_resume.get('name', 'Professional Name')
    contact_info = parsed_resume.get('contact', 'Contact Information')
    original_title = parsed_resume.get('title', '')
    original_summary = parsed_resume.get('summary', '')

    # Create tailored summary based on job requirements and original content
    role_keywords = [kw for kw in summary_keywords if kw in ['consultant', 'manager', 'engineer', 'analyst', 'developer', 'strategist', 'sales']]

    # Use original title if available, otherwise create one
    if original_title:
        tailored_title = original_title
    else:
        primary_role = role_keywords[0].title() if role_keywords else 'Professional'
        tailored_title = f"**{primary_role} | Digital Innovation Specialist**"

    # Create tailored summary
    if original_summary:
        # Enhance the original summary with job-relevant keywords
        tailored_summary = enhance_summary_with_keywords(original_summary, summary_keywords)
    else:
        company_name = extract_company_name(job_description)
        tailored_summary = f"""Accomplished professional with proven expertise in {', '.join(summary_keywords[:3])}. Demonstrated track record of delivering impactful solutions that drive business growth and operational excellence. {f'Seeking to contribute to {company_name}' if company_name else 'Seeking to apply expertise'} by leveraging deep technical knowledge and strategic thinking to achieve organizational objectives."""

    # 2.2. Contextual Proof & Quantifiable Impact (CAR + Why)
    processed_experience = create_experience_from_parsed(parsed_resume.get('experience', []), summary_keywords, key_achievements)

    # 2.3. Authentic Human Voice & 2.4. Cultural & Linguistic Alignment
    # These are largely handled by careful prompt engineering in a real LLM scenario.
    # For this script, we'll aim for a professional, direct tone.

    # 2.5. Signature Project/Achievement Highlight
    signature_highlight = ""
    if key_achievements:
        signature_highlight = f"**Signature Achievement:** {key_achievements[0]}. " \
                              f"This directly aligns with the {job_description.split('.')[0].lower()} requirements."

    # --- 3. Output Requirements & Format ---

    # 3.1. Format: Markdown
    # 3.2. Resume Structure (Enhanced with parsed content)

    # Use parsed contact info
    if contact_info and contact_info != 'Contact Information':
        header_info = f"# {name}\n{contact_info}"
    else:
        header_info = f"# {name}\nProfessional Contact Information"

    # Create sections from parsed content
    skills_section = create_skills_section_from_parsed(parsed_resume.get('skills', []), summary_keywords)
    education_section = create_education_section_from_parsed(parsed_resume.get('education', []))
    languages_section = create_languages_section_from_parsed(parsed_resume.get('languages', []))

    # Add projects and awards if available
    projects_section = ""
    if parsed_resume.get('projects'):
        projects_section = create_projects_section(parsed_resume.get('projects', []))

    awards_section = ""
    if parsed_resume.get('awards'):
        awards_section = create_awards_section(parsed_resume.get('awards', []))

    tailored_resume_markdown = f"""{header_info}

{tailored_title}

{tailored_summary}

## WORK EXPERIENCE
{processed_experience}

{skills_section}

{education_section}

{languages_section}

{projects_section}

{awards_section}
""".strip()

    # --- 3.3. Content & Style ---
    # Ensured by the generation logic (simplified here).

    # --- 3.4. Deliverables ---
    # 1. The Tailored Resume (already generated above)

    # 2. Match Score & Rationale (Simplified)
    match_score = calculate_match_score(job_description, tailored_resume_markdown)
    rationale_improvements = "Key improvements include tailoring the summary to job description keywords and highlighting relevant experience."
    rationale_further_personalization = "Further personalization could involve adding specific quantifiable metrics if available."
    match_rationale = f"""
Match Score: {match_score}/100

Rationale:
- {rationale_improvements}
- {rationale_further_personalization}
"""

    # 3. Strategic Rationale (Simplified)
    strategic_rationale_text = f"""
Strategic Rationale:
- The summary was rephrased to align with the job description's emphasis on {summary_keywords[0]} and {summary_keywords[1]}.
- A signature achievement related to {key_achievements[0] if key_achievements else 'driving impact'} was highlighted to demonstrate direct relevance.
- Experience bullet points were re-evaluated for stronger action verbs and potential for quantifiable results.
"""

    return {
        'tailored_resume_markdown': tailored_resume_markdown,
        'match_score': match_score,
        'match_rationale': match_rationale,
        'strategic_rationale': strategic_rationale_text
    }

def extract_keywords(text: str, num_keywords: int = 5) -> list:
    """
    Enhanced keyword extraction that prioritizes technical terms and important concepts.
    """
    words = re.findall(r'\b\w+\b', text.lower())

    # Expanded common words to filter out
    common_words = {
        'a', 'an', 'the', 'and', 'or', 'in', 'on', 'at', 'for', 'with', 'to', 'of', 'is', 'are', 'be', 'by',
        'this', 'that', 'these', 'those', 'will', 'would', 'could', 'should', 'can', 'may', 'might',
        'have', 'has', 'had', 'do', 'does', 'did', 'get', 'got', 'make', 'made', 'take', 'took',
        'go', 'went', 'come', 'came', 'see', 'saw', 'know', 'knew', 'think', 'thought', 'say', 'said',
        'work', 'working', 'worked', 'job', 'position', 'role', 'company', 'team', 'project', 'projects'
    }

    # Technical and business terms that should be prioritized
    priority_terms = {
        'python', 'javascript', 'react', 'node', 'sql', 'database', 'api', 'cloud', 'aws', 'azure',
        'machine', 'learning', 'ai', 'artificial', 'intelligence', 'data', 'analytics', 'science',
        'management', 'strategy', 'consulting', 'product', 'digital', 'transformation', 'agile',
        'scrum', 'leadership', 'optimization', 'automation', 'integration', 'development',
        'engineering', 'architecture', 'design', 'analysis', 'implementation', 'deployment'
    }

    # Filter and score words
    word_scores = {}
    for word in words:
        if len(word) > 2 and word not in common_words:
            score = 1
            if word in priority_terms:
                score = 3
            elif word.endswith(('ing', 'ed', 'er', 'ly')):
                score = 0.5
            word_scores[word] = word_scores.get(word, 0) + score

    # Sort by score and frequency, return top keywords
    sorted_words = sorted(word_scores.items(), key=lambda x: x[1], reverse=True)
    keywords = [word for word, score in sorted_words[:num_keywords]]
    return keywords

def process_experience_section(base_content: str, job_description: str, key_achievements: list) -> str:
    """
    Enhanced function to process and tailor the experience section based on job requirements.
    """
    # Extract job keywords for tailoring
    jd_keywords = extract_keywords(job_description, num_keywords=15)

    # Try to parse existing experience from base content
    parsed_experience = parse_resume_content(base_content)

    if parsed_experience.get('experience'):
        # Use parsed experience and enhance it
        return enhance_experience_section(parsed_experience['experience'], jd_keywords, key_achievements)
    else:
        # Fallback to template with user's information if available
        return create_template_experience(base_content, jd_keywords, key_achievements)

def parse_resume_content(content: str) -> dict:
    """
    Parse resume content specifically designed for Alex's resume format.
    """
    parsed = {
        'name': '',
        'contact': '',
        'title': '',
        'summary': '',
        'experience': [],
        'skills': [],
        'education': [],
        'languages': [],
        'awards': [],
        'projects': []
    }

    # Handle the case where PDF extraction creates one long line
    if '\n' not in content and len(content) > 500:
        content = restructure_pdf_text(content)

    # Split into sections based on the actual structure
    sections = split_into_sections(content)

    # Parse each section
    for section_name, section_content in sections.items():
        if section_name == 'header':
            parse_header_section(section_content, parsed)
        elif section_name == 'experience':
            parsed['experience'] = parse_experience_section_new(section_content)
        elif section_name == 'skills':
            parsed['skills'] = parse_skills_section_new(section_content)
        elif section_name == 'education':
            parsed['education'] = parse_education_section_new(section_content)
        elif section_name == 'languages':
            parsed['languages'] = parse_languages_section_new(section_content)
        elif section_name == 'awards':
            parsed['awards'] = parse_awards_section_new(section_content)
        elif section_name == 'projects':
            parsed['projects'] = parse_projects_section_new(section_content)

    return parsed

def restructure_pdf_text(content: str) -> str:
    """
    Restructure PDF text that was extracted as one long line.
    """
    # Add line breaks before major sections
    content = re.sub(r'(WORK EXPERIENCE|SKILLS & TOOLS|SKILLS|EDUCATION|LANGUAGES|AWARDS|PROJECT HIGHLIGHT)',
                     r'\n\n\1\n', content, flags=re.IGNORECASE)

    # Add line breaks before job entries (look for patterns like "Job Title Company•Location Date")
    content = re.sub(r'([A-Z][a-z\s&|]+(?:Manager|Consultant|Designer|Lead|Engineer|Analyst|Strategist))\s+([A-Z][A-Za-z\s&]+)•',
                     r'\n\n\1\n\2•', content)

    # Add line breaks before bullet points
    content = re.sub(r'•([^•]+)', r'\n•\1', content)

    # Add line breaks before company names with locations and dates
    content = re.sub(r'([A-Za-z\s&]+)•([A-Za-z\s,/]+)\s+(\d{2}/\d{4}|\d{4})\s*-\s*(Present|\d{2}/\d{4}|\d{4})',
                     r'\n\1•\2 \3 - \4', content)

    # Clean up multiple line breaks
    content = re.sub(r'\n{3,}', '\n\n', content)
    content = re.sub(r' *\n *', '\n', content)

    return content.strip()

def split_into_sections(content: str) -> dict:
    """
    Split content into logical sections.
    """
    sections = {}

    # Find section boundaries
    section_markers = [
        ('WORK EXPERIENCE', 'experience'),
        ('SKILLS & TOOLS', 'skills'),
        ('SKILLS', 'skills'),
        ('EDUCATION', 'education'),
        ('LANGUAGES', 'languages'),
        ('AWARDS', 'awards'),
        ('PROJECT HIGHLIGHT', 'projects')
    ]

    # Find the start of the first major section to separate header
    first_section_pos = len(content)
    for marker, _ in section_markers:
        pos = content.upper().find(marker)
        if pos != -1 and pos < first_section_pos:
            first_section_pos = pos

    # Header section (everything before first major section)
    if first_section_pos > 0:
        sections['header'] = content[:first_section_pos].strip()

    # Split remaining content by sections
    remaining_content = content[first_section_pos:] if first_section_pos < len(content) else ""

    for i, (marker, section_name) in enumerate(section_markers):
        start_pos = remaining_content.upper().find(marker)
        if start_pos == -1:
            continue

        # Find the end position (start of next section or end of content)
        end_pos = len(remaining_content)
        for j, (next_marker, _) in enumerate(section_markers[i+1:], i+1):
            next_pos = remaining_content.upper().find(next_marker)
            if next_pos != -1 and next_pos > start_pos:
                end_pos = next_pos
                break

        # Extract section content
        section_content = remaining_content[start_pos + len(marker):end_pos].strip()
        sections[section_name] = section_content

    return sections

def parse_header_section(content: str, parsed: dict):
    """
    Parse the header section specifically for Alex's resume format.
    """
    lines = [line.strip() for line in content.split('\n') if line.strip()]

    if not lines:
        return

    # First line is the name
    parsed['name'] = lines[0]

    # Combine contact information lines (they might be split)
    contact_lines = []
    title_line = ""
    summary_lines = []

    for line in lines[1:]:
        # Contact information (contains @, •, +, linkedin)
        if any(char in line for char in ['@', '•', '+', 'linkedin']):
            contact_lines.append(line)
        # Title line (contains | and job-related keywords)
        elif ('|' in line and
              any(keyword in line.lower() for keyword in ['strategist', 'sales', 'ai', 'genai', 'adoption', 'growth'])):
            title_line = line
        # Summary (longer descriptive text)
        elif (len(line) > 30 and
              not any(char in line for char in ['@', '+', 'linkedin']) and
              '|' not in line and
              any(keyword in line.lower() for keyword in ['build', 'sell', 'ai', 'markets', 'strategy', 'help', 'teams'])):
            summary_lines.append(line)

    # Combine contact information
    if contact_lines:
        # Clean up and combine contact lines
        contact_text = ' '.join(contact_lines)
        # Clean up extra spaces and formatting
        contact_text = re.sub(r'\s+', ' ', contact_text)
        contact_text = re.sub(r'\s*•\s*', ' • ', contact_text)
        parsed['contact'] = contact_text.strip()

    # Set title
    if title_line:
        parsed['title'] = title_line.strip()

    # Combine summary
    if summary_lines:
        parsed['summary'] = ' '.join(summary_lines).strip()

def parse_experience_section_new(content: str) -> list:
    """
    Parse experience section specifically for Alex's resume format.
    """
    experiences = []

    # Clean up the content first
    content = content.strip()

    # Split into job blocks - look for patterns that indicate new jobs
    # Alex's format: Job Title [newline] Company • Location Date - Date

    # First, let's identify job boundaries by looking for job titles
    job_title_patterns = [
        r'Account Manager',
        r'AI Solutions Consultant',
        r'Digital Experience Designer',
        r'Project Lead',
        r'Design & Research Innovation Lead'
    ]

    # Split content into potential job blocks
    lines = content.split('\n')
    current_job = None
    jobs = []

    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if not line:
            i += 1
            continue

        # Check if this line is a job title
        is_job_title = any(pattern in line for pattern in job_title_patterns)

        if is_job_title:
            # Save previous job if exists
            if current_job:
                jobs.append(current_job)

            # Start new job
            current_job = {
                'title': line,
                'company': '',
                'location': '',
                'dates': '',
                'bullets': []
            }

            # Look for company info in next few lines
            j = i + 1
            while j < len(lines) and j < i + 3:  # Check next 2 lines
                next_line = lines[j].strip()
                if next_line and '•' in next_line:
                    # This looks like company info: Company • Location Date - Date
                    parts = next_line.split('•')
                    if len(parts) >= 2:
                        current_job['company'] = parts[0].strip()
                        location_date = parts[1].strip()

                        # Extract dates
                        date_match = re.search(r'(\d{2}/\d{4}|\d{4})\s*-\s*(Present|\d{2}/\d{4}|\d{4})', location_date)
                        if date_match:
                            current_job['dates'] = date_match.group(0)
                            current_job['location'] = location_date.replace(date_match.group(0), '').strip()
                        else:
                            current_job['location'] = location_date
                    i = j  # Skip this line since we processed it
                    break
                j += 1

        elif current_job and line.startswith('•'):
            # This is a bullet point
            current_job['bullets'].append(line[1:].strip())

        elif current_job and line and not any(pattern in line for pattern in job_title_patterns):
            # This might be a continuation of a bullet or additional info
            if current_job['bullets']:
                # Append to last bullet if it seems like a continuation
                current_job['bullets'][-1] += ' ' + line
            else:
                # Treat as a bullet point
                current_job['bullets'].append(line)

        i += 1

    # Don't forget the last job
    if current_job:
        jobs.append(current_job)

    return jobs

def parse_skills_section_new(content: str) -> list:
    """
    Parse skills section with Alex's format.
    """
    skills = []
    lines = [line.strip() for line in content.split('\n') if line.strip()]

    for line in lines:
        if line:
            skills.append(line)

    return skills

def parse_education_section_new(content: str) -> list:
    """
    Parse education section.
    """
    education = []
    lines = [line.strip() for line in content.split('\n') if line.strip()]

    current_entry = ""
    for line in lines:
        if any(degree in line.lower() for degree in ['master', 'bachelor', 'phd', 'doctorate']):
            if current_entry:
                education.append(current_entry.strip())
            current_entry = line
        else:
            current_entry += " " + line

    if current_entry:
        education.append(current_entry.strip())

    return education

def parse_languages_section_new(content: str) -> list:
    """
    Parse languages section.
    """
    languages = []

    # Look for pattern: "Languages: English (Fluent), French (Native)"
    if 'Languages:' in content:
        lang_text = content.split('Languages:')[1].strip()
        # Split by commas and clean up
        lang_list = [lang.strip() for lang in lang_text.split(',') if lang.strip()]
        languages.extend(lang_list)
    else:
        # Fallback: treat each line as a language
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        languages.extend(lines)

    return languages

def parse_awards_section_new(content: str) -> list:
    """
    Parse awards section.
    """
    awards = []
    lines = [line.strip() for line in content.split('\n') if line.strip()]

    for line in lines:
        if line:
            awards.append(line)

    return awards

def parse_projects_section_new(content: str) -> list:
    """
    Parse project highlights section.
    """
    projects = []
    lines = [line.strip() for line in content.split('\n') if line.strip()]

    current_project = ""
    for line in lines:
        if line:
            current_project += line + " "

    if current_project:
        projects.append(current_project.strip())

    return projects

def is_section_header(line: str, section_type: str) -> bool:
    """
    Determine if a line is a section header for the given section type.
    """
    line_lower = line.lower().strip()

    # Remove common formatting characters
    clean_line = re.sub(r'[•\-=_*#]', '', line_lower).strip()

    section_keywords = {
        'experience': ['work experience', 'professional experience', 'employment', 'career history', 'work history'],
        'skills': ['skills', 'technical skills', 'competencies', 'expertise', 'tools', 'technologies'],
        'education': ['education', 'academic background', 'qualifications', 'degrees', 'certifications'],
        'languages': ['languages', 'language skills', 'linguistic abilities'],
        'summary': ['summary', 'profile', 'objective', 'about', 'overview']
    }

    keywords = section_keywords.get(section_type, [])

    # Check if line matches section keywords
    for keyword in keywords:
        if keyword in clean_line:
            return True

    # Check for exact matches or very close matches
    if section_type == 'experience' and clean_line in ['experience', 'work', 'employment']:
        return True
    elif section_type == 'skills' and clean_line in ['skills', 'technical', 'tools']:
        return True
    elif section_type == 'education' and clean_line in ['education', 'academic', 'degrees']:
        return True
    elif section_type == 'languages' and clean_line in ['languages', 'language']:
        return True
    elif section_type == 'summary' and clean_line in ['summary', 'profile', 'about']:
        return True

    return False

def enhance_experience_section(experience_lines: list, jd_keywords: list, key_achievements: list) -> str:
    """
    Enhanced experience processing that properly parses job entries.
    """
    if not experience_lines:
        return create_template_experience("", jd_keywords, key_achievements)

    enhanced_experience = ""
    jobs = parse_job_entries(experience_lines)

    for i, job in enumerate(jobs):
        if not job:
            continue

        # Extract job details
        job_title = job.get('title', 'Professional Role')
        company = job.get('company', 'Company')
        location = job.get('location', '')
        dates = job.get('dates', 'Dates')
        bullets = job.get('bullets', [])

        # Format job header
        if location:
            job_header = f"**{job_title}** | {company}, {location} | {dates}"
        else:
            job_header = f"**{job_title}** | {company} | {dates}"

        enhanced_experience += f"\n### {company}\n{job_header}\n"

        # Add key achievement for first job
        if i == 0 and key_achievements:
            enhanced_experience += f"*   **Key Achievement:** {key_achievements[0]}\n"

        # Process bullets
        if bullets:
            for bullet in bullets:
                if bullet.strip():
                    enhanced_bullet = enhance_bullet_point(bullet, jd_keywords)
                    enhanced_experience += f"*   {enhanced_bullet}\n"
        else:
            # Create template bullets if none exist
            enhanced_experience += create_template_bullets(jd_keywords)

        enhanced_experience += "\n"

    return enhanced_experience

def parse_job_entries(experience_lines: list) -> list:
    """
    Parse experience lines into structured job entries.
    """
    jobs = []
    current_job = None

    for line in experience_lines:
        line = line.strip()
        if not line:
            continue

        # Check if this line looks like a job header
        if is_job_header(line):
            # Save previous job if exists
            if current_job:
                jobs.append(current_job)

            # Parse the job header
            current_job = parse_job_header(line)
        elif line.startswith('•') or line.startswith('-') or line.startswith('*'):
            # This is a bullet point
            if current_job:
                bullet = line.lstrip('•-* ').strip()
                current_job['bullets'].append(bullet)
        elif current_job and len(line.split()) > 3:
            # This might be a continuation or additional bullet
            current_job['bullets'].append(line)

    # Don't forget the last job
    if current_job:
        jobs.append(current_job)

    return jobs

def is_job_header(line: str) -> bool:
    """
    Determine if a line is likely a job header.
    """
    # Look for patterns that indicate job headers
    indicators = [
        '|',  # Common separator
        '•',  # Bullet separator
        'present',  # Date indicator
        '20',  # Year indicator
        '19',  # Year indicator
    ]

    line_lower = line.lower()

    # Must have some date-like content or separators
    has_date_indicator = any(indicator in line_lower for indicator in ['present', '20', '19'])
    has_separator = any(sep in line for sep in ['|', '•', '-'])

    # Should not be too long (job headers are usually concise)
    reasonable_length = len(line.split()) <= 15

    return (has_date_indicator or has_separator) and reasonable_length

def parse_job_header(line: str) -> dict:
    """
    Parse a job header line into components.
    """
    job = {
        'title': '',
        'company': '',
        'location': '',
        'dates': '',
        'bullets': []
    }

    # Try different separator patterns
    if '|' in line:
        parts = [part.strip() for part in line.split('|')]
    elif '•' in line:
        parts = [part.strip() for part in line.split('•')]
    else:
        # Try to split on common patterns
        parts = [line.strip()]

    if len(parts) >= 3:
        # Format: Title | Company | Dates
        job['title'] = parts[0]
        job['company'] = parts[1]
        job['dates'] = parts[2]
        if len(parts) > 3:
            job['location'] = parts[2]
            job['dates'] = parts[3]
    elif len(parts) == 2:
        # Format: Title | Company or Company | Dates
        if any(date_word in parts[1].lower() for date_word in ['present', '20', '19']):
            job['company'] = parts[0]
            job['dates'] = parts[1]
        else:
            job['title'] = parts[0]
            job['company'] = parts[1]
    else:
        # Single part - try to extract what we can
        job['title'] = parts[0]

    return job

def enhance_bullet_point(bullet: str, jd_keywords: list) -> str:
    """
    Enhance a bullet point to include relevant keywords and stronger action verbs.
    """
    # Strong action verbs
    action_verbs = [
        'Led', 'Developed', 'Implemented', 'Optimized', 'Managed', 'Created', 'Designed',
        'Delivered', 'Achieved', 'Improved', 'Increased', 'Reduced', 'Streamlined',
        'Collaborated', 'Coordinated', 'Executed', 'Established', 'Transformed'
    ]

    # Clean up the bullet
    bullet = bullet.strip('*- ').strip()

    # If bullet doesn't start with a strong action verb, try to improve it
    if not any(bullet.startswith(verb) for verb in action_verbs):
        # Try to identify the action and replace with stronger verb
        if 'worked' in bullet.lower():
            bullet = bullet.replace('worked', 'collaborated', 1)
        elif 'did' in bullet.lower():
            bullet = bullet.replace('did', 'executed', 1)
        elif 'made' in bullet.lower():
            bullet = bullet.replace('made', 'created', 1)

    # Ensure it starts with capital letter
    if bullet:
        bullet = bullet[0].upper() + bullet[1:]

    return bullet

def create_template_bullets(jd_keywords: list) -> str:
    """
    Create template bullet points based on job keywords.
    """
    bullets = ""

    # Create bullets that incorporate job keywords
    if any(keyword in jd_keywords for keyword in ['data', 'analysis', 'analytics']):
        bullets += "*   Analyzed complex datasets to drive strategic decision-making and business insights\n"

    if any(keyword in jd_keywords for keyword in ['team', 'leadership', 'manage']):
        bullets += "*   Led cross-functional teams to deliver high-impact projects on time and within budget\n"

    if any(keyword in jd_keywords for keyword in ['product', 'development', 'software']):
        bullets += "*   Developed and launched innovative solutions that improved operational efficiency\n"

    if any(keyword in jd_keywords for keyword in ['client', 'customer', 'stakeholder']):
        bullets += "*   Collaborated with key stakeholders to understand requirements and deliver tailored solutions\n"

    # Default bullet if no specific keywords match
    if not bullets:
        bullets = "*   Successfully delivered key initiatives that contributed to organizational goals\n"

    return bullets

def create_template_experience(base_content: str, jd_keywords: list, key_achievements: list) -> str:
    """
    Create a template experience section when parsing fails.
    """
    # Try to extract any company or role information from base content
    lines = base_content.split('\n')
    company_info = []

    for line in lines:
        if any(keyword in line.lower() for keyword in ['company', 'corp', 'inc', 'ltd', 'gmbh', 'consultant', 'manager', 'engineer', 'analyst']):
            company_info.append(line.strip())

    experience = ""

    if company_info:
        for info in company_info[:2]:  # Use up to 2 pieces of company info
            experience += f"\n### {info}\n"
            if key_achievements:
                experience += f"*   **Key Achievement:** {key_achievements.pop(0)}\n"
            experience += create_template_bullets(jd_keywords)
            experience += "\n"
    else:
        # Complete fallback template
        experience = f"""
### Current/Recent Position
**Professional Role** | Recent Period
{f'*   **Key Achievement:** {key_achievements[0]}' if key_achievements else ''}
{create_template_bullets(jd_keywords)}

### Previous Position
**Previous Role** | Previous Period
*   Delivered impactful results in fast-paced environment
*   Collaborated with diverse teams to achieve organizational objectives
"""

    return experience

def enhance_summary_with_keywords(original_summary: str, keywords: list) -> str:
    """
    Enhance the original summary by incorporating relevant keywords.
    """
    enhanced = original_summary

    # Add relevant keywords naturally if they're not already present
    for keyword in keywords[:3]:
        if keyword.lower() not in enhanced.lower():
            # Try to add the keyword naturally
            if 'experience' in enhanced.lower():
                enhanced = enhanced.replace('experience', f'experience in {keyword}', 1)
                break

    return enhanced

def create_experience_from_parsed(experiences: list, keywords: list, key_achievements: list) -> str:
    """
    Create experience section from parsed experience data.
    """
    if not experiences:
        return "No work experience data available."

    experience_md = ""

    for i, exp in enumerate(experiences):
        if not exp:
            continue

        # Format job header
        title = exp.get('title', 'Professional Role')
        company = exp.get('company', 'Company')
        location = exp.get('location', '')
        dates = exp.get('dates', 'Dates')
        bullets = exp.get('bullets', [])

        # Create job header
        if location:
            job_header = f"**{title}** | {company}, {location} | {dates}"
        else:
            job_header = f"**{title}** | {company} | {dates}"

        experience_md += f"\n### {company}\n{job_header}\n"

        # Add key achievement for first job
        if i == 0 and key_achievements:
            experience_md += f"*   **Key Achievement:** {key_achievements[0]}\n"

        # Add bullets
        for bullet in bullets:
            if bullet.strip():
                enhanced_bullet = enhance_bullet_point(bullet, keywords)
                experience_md += f"*   {enhanced_bullet}\n"

        experience_md += "\n"

    return experience_md

def create_skills_section_from_parsed(skills: list, keywords: list) -> str:
    """
    Create skills section from parsed skills.
    """
    if not skills:
        return """## SKILLS
*   **Technical:** Python, SQL, Data Analysis
*   **Business:** Strategic Planning, Project Management
"""

    skills_md = "## SKILLS\n"

    # Process skills - they might be in different formats
    all_skills_text = ' '.join(skills)

    # Look for categorized skills
    if 'GPT' in all_skills_text or 'Claude' in all_skills_text:
        # AI/Tech skills
        ai_skills = extract_skills_by_category(all_skills_text, ['GPT', 'Claude', 'n8n', 'Zapier', 'firecrawl'])
        if ai_skills:
            skills_md += f"*   **AI & Automation:** {', '.join(ai_skills)}\n"

    # Business skills
    business_skills = extract_skills_by_category(all_skills_text, ['sales', 'account', 'pipeline', 'pitching', 'market'])
    if business_skills:
        skills_md += f"*   **Business & Sales:** {', '.join(business_skills)}\n"

    # Technical tools
    tech_skills = extract_skills_by_category(all_skills_text, ['Notion', 'Airtable', 'Supabase', 'HubSpot', 'Mixpanel'])
    if tech_skills:
        skills_md += f"*   **Tools & Platforms:** {', '.join(tech_skills)}\n"

    # If no categorized skills found, just list them
    if skills_md == "## SKILLS\n":
        for skill_line in skills:
            if skill_line.strip():
                skills_md += f"*   {skill_line.strip()}\n"

    return skills_md

def extract_skills_by_category(text: str, category_keywords: list) -> list:
    """
    Extract skills that match category keywords.
    """
    found_skills = []
    for keyword in category_keywords:
        if keyword.lower() in text.lower():
            found_skills.append(keyword)
    return found_skills

def create_education_section_from_parsed(education: list) -> str:
    """
    Create education section from parsed education.
    """
    if not education:
        return """## EDUCATION
*   **Relevant Degree**, Institution, Year
"""

    education_md = "## EDUCATION\n"
    for edu in education:
        if edu.strip():
            education_md += f"*   **{edu.strip()}**\n"

    return education_md

def create_languages_section_from_parsed(languages: list) -> str:
    """
    Create languages section from parsed languages.
    """
    if not languages:
        return """## LANGUAGES
*   English (Professional)
*   Additional languages as applicable
"""

    languages_md = "## LANGUAGES\n"
    for lang in languages:
        if lang.strip():
            languages_md += f"*   {lang.strip()}\n"

    return languages_md

def create_projects_section(projects: list) -> str:
    """
    Create projects section.
    """
    if not projects:
        return ""

    projects_md = "## PROJECT HIGHLIGHTS\n"
    for project in projects:
        if project.strip():
            projects_md += f"*   {project.strip()}\n"

    return projects_md

def create_awards_section(awards: list) -> str:
    """
    Create awards section.
    """
    if not awards:
        return ""

    awards_md = "## AWARDS\n"
    for award in awards:
        if award.strip():
            awards_md += f"*   {award.strip()}\n"

    return awards_md

def extract_company_name(job_description: str) -> str:
    """
    Try to extract company name from job description.
    """
    lines = job_description.split('\n')
    for line in lines[:5]:  # Check first few lines
        line = line.strip()
        if line and not line.lower().startswith(('we are', 'job', 'position', 'role', 'about')):
            # Simple heuristic: if it's a short line early in the description, might be company name
            if len(line.split()) <= 4 and len(line) > 2:
                return line
    return ""

def create_skills_section(parsed_skills: list, jd_keywords: list) -> str:
    """
    Create a tailored skills section based on parsed skills and job requirements.
    """
    technical_skills = []
    business_skills = []
    tools_skills = []

    # Process parsed skills
    for skill_line in parsed_skills:
        if not skill_line.strip():
            continue

        # Clean up the line
        clean_line = skill_line.strip()

        # Check if it's a categorized line (e.g., "Programming Languages: Python, Java")
        if ':' in clean_line:
            category, skills_text = clean_line.split(':', 1)
            category_lower = category.lower().strip()
            skills_list = [s.strip() for s in skills_text.split(',') if s.strip()]

            if any(tech_word in category_lower for tech_word in ['programming', 'technical', 'languages', 'frameworks', 'databases']):
                technical_skills.extend(skills_list)
            elif any(tool_word in category_lower for tool_word in ['tools', 'platforms', 'cloud', 'software']):
                tools_skills.extend(skills_list)
            else:
                business_skills.extend(skills_list)
        else:
            # Process as comma-separated skills
            skills_list = [s.strip() for s in clean_line.split(',') if s.strip()]

            # Categorize based on content
            for skill in skills_list:
                skill_lower = skill.lower()
                if any(tech in skill_lower for tech in ['python', 'java', 'javascript', 'sql', 'react', 'node', 'django', 'flask']):
                    technical_skills.append(skill)
                elif any(tool in skill_lower for tool in ['aws', 'azure', 'docker', 'kubernetes', 'git', 'jenkins', 'mongodb']):
                    tools_skills.append(skill)
                elif any(biz in skill_lower for biz in ['management', 'strategy', 'consulting', 'leadership', 'analysis', 'planning']):
                    business_skills.append(skill)
                else:
                    # Default to technical
                    technical_skills.append(skill)

    # Add job-relevant skills
    for keyword in jd_keywords[:5]:
        keyword_title = keyword.title()
        all_skills = technical_skills + business_skills + tools_skills

        if keyword.lower() not in [s.lower() for s in all_skills]:
            if keyword.lower() in ['python', 'sql', 'javascript', 'react', 'java', 'c++', 'machine', 'learning', 'ai']:
                technical_skills.append(keyword_title)
            elif keyword.lower() in ['aws', 'azure', 'docker', 'kubernetes', 'git', 'cloud']:
                tools_skills.append(keyword_title)
            else:
                business_skills.append(keyword_title)

    # Build skills section
    skills_section = "## SKILLS\n"

    if technical_skills:
        skills_section += f"*   **Technical:** {', '.join(technical_skills[:8])}\n"
    if tools_skills:
        skills_section += f"*   **Tools & Platforms:** {', '.join(tools_skills[:8])}\n"
    if business_skills:
        skills_section += f"*   **Business:** {', '.join(business_skills[:8])}\n"

    # Fallback if no skills found
    if not technical_skills and not tools_skills and not business_skills:
        skills_section += "*   **Technical:** Python, SQL, Data Analysis\n"
        skills_section += "*   **Business:** Strategic Planning, Project Management\n"

    return skills_section

def create_languages_section(parsed_languages: list) -> str:
    """
    Create languages section from parsed content or use defaults.
    """
    if parsed_languages:
        languages_section = "## LANGUAGES\n"
        for lang_line in parsed_languages:
            if lang_line.strip():
                languages_section += f"*   {lang_line.strip()}\n"
        return languages_section
    else:
        return """## LANGUAGES
*   English (Professional)
*   Additional languages as applicable
"""

def create_education_section(parsed_education: list) -> str:
    """
    Create education section from parsed content with better formatting.
    """
    if not parsed_education:
        return """## EDUCATION
*   **Relevant Degree**, Institution, Year
*   **Additional Qualifications** as applicable
"""

    education_section = "## EDUCATION\n"

    for edu_line in parsed_education:
        edu_line = edu_line.strip()
        if not edu_line:
            continue

        # Try to format education entries better
        if any(degree in edu_line.lower() for degree in ['bachelor', 'master', 'phd', 'doctorate', 'degree']):
            # This looks like a degree line
            education_section += f"*   **{edu_line}**\n"
        elif any(cert in edu_line.lower() for cert in ['certified', 'certification', 'certificate']):
            # This looks like a certification
            education_section += f"*   {edu_line}\n"
        else:
            # Generic education entry
            education_section += f"*   {edu_line}\n"

    return education_section

def calculate_match_score(job_description: str, resume_content: str) -> int:
    """
    Enhanced match score calculation based on keyword overlap and relevance.
    """
    jd_keywords = set(extract_keywords(job_description, num_keywords=20))
    resume_keywords = set(extract_keywords(resume_content, num_keywords=50))

    common_keywords = jd_keywords.intersection(resume_keywords)

    # Base score from keyword overlap
    base_score = int((len(common_keywords) / len(jd_keywords)) * 100) if len(jd_keywords) > 0 else 0

    # Bonus points for high-value keywords
    high_value_keywords = {'python', 'sql', 'machine', 'learning', 'ai', 'management', 'strategy', 'consulting', 'leadership'}
    high_value_matches = common_keywords.intersection(high_value_keywords)
    bonus_score = len(high_value_matches) * 5

    final_score = min(base_score + bonus_score, 100)
    return final_score


if __name__ == "__main__":
    # Example Usage:
    sample_job_description = """
    We are seeking a highly motivated AI Consultant with strong experience in machine learning, 
    data analysis, and strategic consulting. The ideal candidate will have a proven track record 
    of delivering impactful AI solutions and working with cross-functional teams. 
    Experience in product management and client-facing roles is a plus.
    """

    sample_base_resume_content = """
    Name: Alex Guyenne
    Summary: AI Consultant | Digital Product Strategist | Account Manager. I help companies automate workflows.
    Experience: Gladtobe (AI Consultant), Previous Company (Digital Product Manager)
    Skills: Python, SQL, Product Management
    Education: Master of Science in AI
    """

    sample_key_achievements = [
        "Led development of an AI assistant for a German telecom, automating content creation and reducing delivery time by 60%."
    ]

    result = generate_resume(
        job_description=sample_job_description,
        base_resume_content=sample_base_resume_content,
        key_achievements=sample_key_achievements
    )

    print("--- Tailored Resume (Markdown) ---")
    print(result['tailored_resume_markdown'])
    print("\n--- Match Score & Rationale ---")
    print(result['match_rationale'])
    print("\n--- Strategic Rationale ---")
    print(result['strategic_rationale'])