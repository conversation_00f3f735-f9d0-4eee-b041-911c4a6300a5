import re

def generate_resume(
    job_description: str,
    base_resume_content: str,
    layout_preference: dict = None,
    key_achievements: list = None,
    specific_concerns: str = None
) -> dict:
    """
    Generates a tailored resume in Markdown format based on the provided inputs.

    Args:
        job_description (str): Full job advertisement text.
        base_resume_content (str): Plain text content of the user's current resume.
        layout_preference (dict): Dictionary describing the preferred resume layout (e.g., section order, bolding).
                                  Currently, this will be a simplified representation.
        key_achievements (list): List of 1-3 specific achievements/projects to highlight.
        specific_concerns (str): User's specific concerns or areas for improvement.

    Returns:
        dict: A dictionary containing:
            - 'tailored_resume_markdown': The generated resume in Markdown format.
            - 'match_score': A numerical score indicating alignment with the job description.
            - 'strategic_rationale': Explanation of key strategic choices.
    """

    # --- 1. Parse Inputs ---
    parsed_resume = parse_resume_content(base_resume_content)

    # --- 2. Core AI Strategy & Guiding Principles (Enhanced Implementation) ---

    # 2.1. Strategic Positioning & Summary Generation
    summary_keywords = extract_keywords(job_description, num_keywords=10)

    # Use parsed name if available, otherwise use default
    name = parsed_resume.get('name', 'Professional Name')
    contact_info = parsed_resume.get('contact', 'Contact Information')

    # Create tailored summary based on job requirements
    role_keywords = [kw for kw in summary_keywords if kw in ['consultant', 'manager', 'engineer', 'analyst', 'developer', 'strategist']]
    primary_role = role_keywords[0].title() if role_keywords else 'Professional'

    # Extract company name from job description if possible
    company_name = extract_company_name(job_description)

    tailored_summary = f"""**{primary_role} | Digital Innovation Specialist**

Accomplished professional with proven expertise in {', '.join(summary_keywords[:3])}. Demonstrated track record of delivering impactful solutions that drive business growth and operational excellence. {f'Seeking to contribute to {company_name}' if company_name else 'Seeking to apply expertise'} by leveraging deep technical knowledge and strategic thinking to achieve organizational objectives."""

    # 2.2. Contextual Proof & Quantifiable Impact (CAR + Why)
    processed_experience = process_experience_section(base_resume_content, job_description, key_achievements)

    # 2.3. Authentic Human Voice & 2.4. Cultural & Linguistic Alignment
    # These are largely handled by careful prompt engineering in a real LLM scenario.
    # For this script, we'll aim for a professional, direct tone.

    # 2.5. Signature Project/Achievement Highlight
    signature_highlight = ""
    if key_achievements:
        signature_highlight = f"**Signature Achievement:** {key_achievements[0]}. " \
                              f"This directly aligns with the {job_description.split('.')[0].lower()} requirements."

    # --- 3. Output Requirements & Format ---

    # 3.1. Format: Markdown
    # 3.2. Resume Structure (Enhanced with parsed content)

    # Use parsed contact info or create from available data
    if contact_info and contact_info != 'Contact Information':
        header_info = f"{name}\n{contact_info}"
    else:
        header_info = f"{name}\nProfessional Contact Information"

    # Create tailored skills section
    skills_section = create_skills_section(parsed_resume.get('skills', []), summary_keywords)

    # Create languages section from parsed content or use default
    languages_section = create_languages_section(parsed_resume.get('languages', []))

    # Create education section from parsed content or use default
    education_section = create_education_section(parsed_resume.get('education', []))

    tailored_resume_markdown = f"""{header_info}

{tailored_summary}

## WORK EXPERIENCE
{processed_experience}

{skills_section}

{languages_section}

{education_section}
"""

    # --- 3.3. Content & Style ---
    # Ensured by the generation logic (simplified here).

    # --- 3.4. Deliverables ---
    # 1. The Tailored Resume (already generated above)

    # 2. Match Score & Rationale (Simplified)
    match_score = calculate_match_score(job_description, tailored_resume_markdown)
    rationale_improvements = "Key improvements include tailoring the summary to job description keywords and highlighting relevant experience."
    rationale_further_personalization = "Further personalization could involve adding specific quantifiable metrics if available."
    match_rationale = f"""
Match Score: {match_score}/100

Rationale:
- {rationale_improvements}
- {rationale_further_personalization}
"""

    # 3. Strategic Rationale (Simplified)
    strategic_rationale_text = f"""
Strategic Rationale:
- The summary was rephrased to align with the job description's emphasis on {summary_keywords[0]} and {summary_keywords[1]}.
- A signature achievement related to {key_achievements[0] if key_achievements else 'driving impact'} was highlighted to demonstrate direct relevance.
- Experience bullet points were re-evaluated for stronger action verbs and potential for quantifiable results.
"""

    return {
        'tailored_resume_markdown': tailored_resume_markdown,
        'match_score': match_score,
        'match_rationale': match_rationale,
        'strategic_rationale': strategic_rationale_text
    }

def extract_keywords(text: str, num_keywords: int = 5) -> list:
    """
    Enhanced keyword extraction that prioritizes technical terms and important concepts.
    """
    words = re.findall(r'\b\w+\b', text.lower())

    # Expanded common words to filter out
    common_words = {
        'a', 'an', 'the', 'and', 'or', 'in', 'on', 'at', 'for', 'with', 'to', 'of', 'is', 'are', 'be', 'by',
        'this', 'that', 'these', 'those', 'will', 'would', 'could', 'should', 'can', 'may', 'might',
        'have', 'has', 'had', 'do', 'does', 'did', 'get', 'got', 'make', 'made', 'take', 'took',
        'go', 'went', 'come', 'came', 'see', 'saw', 'know', 'knew', 'think', 'thought', 'say', 'said',
        'work', 'working', 'worked', 'job', 'position', 'role', 'company', 'team', 'project', 'projects'
    }

    # Technical and business terms that should be prioritized
    priority_terms = {
        'python', 'javascript', 'react', 'node', 'sql', 'database', 'api', 'cloud', 'aws', 'azure',
        'machine', 'learning', 'ai', 'artificial', 'intelligence', 'data', 'analytics', 'science',
        'management', 'strategy', 'consulting', 'product', 'digital', 'transformation', 'agile',
        'scrum', 'leadership', 'optimization', 'automation', 'integration', 'development',
        'engineering', 'architecture', 'design', 'analysis', 'implementation', 'deployment'
    }

    # Filter and score words
    word_scores = {}
    for word in words:
        if len(word) > 2 and word not in common_words:
            score = 1
            if word in priority_terms:
                score = 3
            elif word.endswith(('ing', 'ed', 'er', 'ly')):
                score = 0.5
            word_scores[word] = word_scores.get(word, 0) + score

    # Sort by score and frequency, return top keywords
    sorted_words = sorted(word_scores.items(), key=lambda x: x[1], reverse=True)
    keywords = [word for word, score in sorted_words[:num_keywords]]
    return keywords

def process_experience_section(base_content: str, job_description: str, key_achievements: list) -> str:
    """
    Enhanced function to process and tailor the experience section based on job requirements.
    """
    # Extract job keywords for tailoring
    jd_keywords = extract_keywords(job_description, num_keywords=15)

    # Try to parse existing experience from base content
    parsed_experience = parse_resume_content(base_content)

    if parsed_experience.get('experience'):
        # Use parsed experience and enhance it
        return enhance_experience_section(parsed_experience['experience'], jd_keywords, key_achievements)
    else:
        # Fallback to template with user's information if available
        return create_template_experience(base_content, jd_keywords, key_achievements)

def parse_resume_content(content: str) -> dict:
    """
    Enhanced resume parser that handles complex resume structures.
    """
    parsed = {
        'name': '',
        'contact': '',
        'summary': '',
        'experience': [],
        'skills': [],
        'education': [],
        'languages': []
    }

    lines = content.split('\n')
    current_section = None

    # First pass: extract name and contact info from the top
    contact_lines = []
    name_found = False

    for i, line in enumerate(lines[:10]):  # Check first 10 lines for header info
        line = line.strip()
        if not line:
            continue

        # Look for name (usually first substantial line)
        if not name_found and len(line.split()) <= 4 and not any(char in line for char in ['@', '•', '|', 'http']):
            if not any(keyword in line.lower() for keyword in ['summary', 'experience', 'work', 'skills', 'education']):
                parsed['name'] = line
                name_found = True
                continue

        # Look for contact information
        if any(indicator in line for indicator in ['@', '•', '|', 'linkedin', 'github', 'phone', '+', '(']):
            contact_lines.append(line)
        elif line.lower().startswith(('email:', 'phone:', 'linkedin:')):
            contact_lines.append(line)

    # Combine contact lines
    if contact_lines:
        parsed['contact'] = ' | '.join(contact_lines)

    # Second pass: parse sections
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue

        line_lower = line.lower()

        # Detect section headers (more robust detection)
        if is_section_header(line, 'experience'):
            current_section = 'experience'
            continue
        elif is_section_header(line, 'skills'):
            current_section = 'skills'
            continue
        elif is_section_header(line, 'education'):
            current_section = 'education'
            continue
        elif is_section_header(line, 'languages'):
            current_section = 'languages'
            continue
        elif is_section_header(line, 'summary'):
            current_section = 'summary'
            continue

        # Skip header lines we already processed
        if line == parsed['name'] or line in contact_lines:
            continue

        # Add content to current section
        if current_section == 'experience':
            parsed['experience'].append(line)
        elif current_section == 'skills':
            parsed['skills'].append(line)
        elif current_section == 'education':
            parsed['education'].append(line)
        elif current_section == 'languages':
            parsed['languages'].append(line)
        elif current_section == 'summary':
            parsed['summary'] += ' ' + line if parsed['summary'] else line

    return parsed

def is_section_header(line: str, section_type: str) -> bool:
    """
    Determine if a line is a section header for the given section type.
    """
    line_lower = line.lower().strip()

    # Remove common formatting characters
    clean_line = re.sub(r'[•\-=_*#]', '', line_lower).strip()

    section_keywords = {
        'experience': ['work experience', 'professional experience', 'employment', 'career history', 'work history'],
        'skills': ['skills', 'technical skills', 'competencies', 'expertise', 'tools', 'technologies'],
        'education': ['education', 'academic background', 'qualifications', 'degrees', 'certifications'],
        'languages': ['languages', 'language skills', 'linguistic abilities'],
        'summary': ['summary', 'profile', 'objective', 'about', 'overview']
    }

    keywords = section_keywords.get(section_type, [])

    # Check if line matches section keywords
    for keyword in keywords:
        if keyword in clean_line:
            return True

    # Check for exact matches or very close matches
    if section_type == 'experience' and clean_line in ['experience', 'work', 'employment']:
        return True
    elif section_type == 'skills' and clean_line in ['skills', 'technical', 'tools']:
        return True
    elif section_type == 'education' and clean_line in ['education', 'academic', 'degrees']:
        return True
    elif section_type == 'languages' and clean_line in ['languages', 'language']:
        return True
    elif section_type == 'summary' and clean_line in ['summary', 'profile', 'about']:
        return True

    return False

def enhance_experience_section(experience_lines: list, jd_keywords: list, key_achievements: list) -> str:
    """
    Enhanced experience processing that properly parses job entries.
    """
    if not experience_lines:
        return create_template_experience("", jd_keywords, key_achievements)

    enhanced_experience = ""
    jobs = parse_job_entries(experience_lines)

    for i, job in enumerate(jobs):
        if not job:
            continue

        # Extract job details
        job_title = job.get('title', 'Professional Role')
        company = job.get('company', 'Company')
        location = job.get('location', '')
        dates = job.get('dates', 'Dates')
        bullets = job.get('bullets', [])

        # Format job header
        if location:
            job_header = f"**{job_title}** | {company}, {location} | {dates}"
        else:
            job_header = f"**{job_title}** | {company} | {dates}"

        enhanced_experience += f"\n### {company}\n{job_header}\n"

        # Add key achievement for first job
        if i == 0 and key_achievements:
            enhanced_experience += f"*   **Key Achievement:** {key_achievements[0]}\n"

        # Process bullets
        if bullets:
            for bullet in bullets:
                if bullet.strip():
                    enhanced_bullet = enhance_bullet_point(bullet, jd_keywords)
                    enhanced_experience += f"*   {enhanced_bullet}\n"
        else:
            # Create template bullets if none exist
            enhanced_experience += create_template_bullets(jd_keywords)

        enhanced_experience += "\n"

    return enhanced_experience

def parse_job_entries(experience_lines: list) -> list:
    """
    Parse experience lines into structured job entries.
    """
    jobs = []
    current_job = None

    for line in experience_lines:
        line = line.strip()
        if not line:
            continue

        # Check if this line looks like a job header
        if is_job_header(line):
            # Save previous job if exists
            if current_job:
                jobs.append(current_job)

            # Parse the job header
            current_job = parse_job_header(line)
        elif line.startswith('•') or line.startswith('-') or line.startswith('*'):
            # This is a bullet point
            if current_job:
                bullet = line.lstrip('•-* ').strip()
                current_job['bullets'].append(bullet)
        elif current_job and len(line.split()) > 3:
            # This might be a continuation or additional bullet
            current_job['bullets'].append(line)

    # Don't forget the last job
    if current_job:
        jobs.append(current_job)

    return jobs

def is_job_header(line: str) -> bool:
    """
    Determine if a line is likely a job header.
    """
    # Look for patterns that indicate job headers
    indicators = [
        '|',  # Common separator
        '•',  # Bullet separator
        'present',  # Date indicator
        '20',  # Year indicator
        '19',  # Year indicator
    ]

    line_lower = line.lower()

    # Must have some date-like content or separators
    has_date_indicator = any(indicator in line_lower for indicator in ['present', '20', '19'])
    has_separator = any(sep in line for sep in ['|', '•', '-'])

    # Should not be too long (job headers are usually concise)
    reasonable_length = len(line.split()) <= 15

    return (has_date_indicator or has_separator) and reasonable_length

def parse_job_header(line: str) -> dict:
    """
    Parse a job header line into components.
    """
    job = {
        'title': '',
        'company': '',
        'location': '',
        'dates': '',
        'bullets': []
    }

    # Try different separator patterns
    if '|' in line:
        parts = [part.strip() for part in line.split('|')]
    elif '•' in line:
        parts = [part.strip() for part in line.split('•')]
    else:
        # Try to split on common patterns
        parts = [line.strip()]

    if len(parts) >= 3:
        # Format: Title | Company | Dates
        job['title'] = parts[0]
        job['company'] = parts[1]
        job['dates'] = parts[2]
        if len(parts) > 3:
            job['location'] = parts[2]
            job['dates'] = parts[3]
    elif len(parts) == 2:
        # Format: Title | Company or Company | Dates
        if any(date_word in parts[1].lower() for date_word in ['present', '20', '19']):
            job['company'] = parts[0]
            job['dates'] = parts[1]
        else:
            job['title'] = parts[0]
            job['company'] = parts[1]
    else:
        # Single part - try to extract what we can
        job['title'] = parts[0]

    return job

def enhance_bullet_point(bullet: str, jd_keywords: list) -> str:
    """
    Enhance a bullet point to include relevant keywords and stronger action verbs.
    """
    # Strong action verbs
    action_verbs = [
        'Led', 'Developed', 'Implemented', 'Optimized', 'Managed', 'Created', 'Designed',
        'Delivered', 'Achieved', 'Improved', 'Increased', 'Reduced', 'Streamlined',
        'Collaborated', 'Coordinated', 'Executed', 'Established', 'Transformed'
    ]

    # Clean up the bullet
    bullet = bullet.strip('*- ').strip()

    # If bullet doesn't start with a strong action verb, try to improve it
    if not any(bullet.startswith(verb) for verb in action_verbs):
        # Try to identify the action and replace with stronger verb
        if 'worked' in bullet.lower():
            bullet = bullet.replace('worked', 'collaborated', 1)
        elif 'did' in bullet.lower():
            bullet = bullet.replace('did', 'executed', 1)
        elif 'made' in bullet.lower():
            bullet = bullet.replace('made', 'created', 1)

    # Ensure it starts with capital letter
    if bullet:
        bullet = bullet[0].upper() + bullet[1:]

    return bullet

def create_template_bullets(jd_keywords: list) -> str:
    """
    Create template bullet points based on job keywords.
    """
    bullets = ""

    # Create bullets that incorporate job keywords
    if any(keyword in jd_keywords for keyword in ['data', 'analysis', 'analytics']):
        bullets += "*   Analyzed complex datasets to drive strategic decision-making and business insights\n"

    if any(keyword in jd_keywords for keyword in ['team', 'leadership', 'manage']):
        bullets += "*   Led cross-functional teams to deliver high-impact projects on time and within budget\n"

    if any(keyword in jd_keywords for keyword in ['product', 'development', 'software']):
        bullets += "*   Developed and launched innovative solutions that improved operational efficiency\n"

    if any(keyword in jd_keywords for keyword in ['client', 'customer', 'stakeholder']):
        bullets += "*   Collaborated with key stakeholders to understand requirements and deliver tailored solutions\n"

    # Default bullet if no specific keywords match
    if not bullets:
        bullets = "*   Successfully delivered key initiatives that contributed to organizational goals\n"

    return bullets

def create_template_experience(base_content: str, jd_keywords: list, key_achievements: list) -> str:
    """
    Create a template experience section when parsing fails.
    """
    # Try to extract any company or role information from base content
    lines = base_content.split('\n')
    company_info = []

    for line in lines:
        if any(keyword in line.lower() for keyword in ['company', 'corp', 'inc', 'ltd', 'gmbh', 'consultant', 'manager', 'engineer', 'analyst']):
            company_info.append(line.strip())

    experience = ""

    if company_info:
        for info in company_info[:2]:  # Use up to 2 pieces of company info
            experience += f"\n### {info}\n"
            if key_achievements:
                experience += f"*   **Key Achievement:** {key_achievements.pop(0)}\n"
            experience += create_template_bullets(jd_keywords)
            experience += "\n"
    else:
        # Complete fallback template
        experience = f"""
### Current/Recent Position
**Professional Role** | Recent Period
{f'*   **Key Achievement:** {key_achievements[0]}' if key_achievements else ''}
{create_template_bullets(jd_keywords)}

### Previous Position
**Previous Role** | Previous Period
*   Delivered impactful results in fast-paced environment
*   Collaborated with diverse teams to achieve organizational objectives
"""

    return experience

def extract_company_name(job_description: str) -> str:
    """
    Try to extract company name from job description.
    """
    lines = job_description.split('\n')
    for line in lines[:5]:  # Check first few lines
        line = line.strip()
        if line and not line.lower().startswith(('we are', 'job', 'position', 'role', 'about')):
            # Simple heuristic: if it's a short line early in the description, might be company name
            if len(line.split()) <= 4 and len(line) > 2:
                return line
    return ""

def create_skills_section(parsed_skills: list, jd_keywords: list) -> str:
    """
    Create a tailored skills section based on parsed skills and job requirements.
    """
    technical_skills = []
    business_skills = []
    tools_skills = []

    # Process parsed skills
    for skill_line in parsed_skills:
        if not skill_line.strip():
            continue

        # Clean up the line
        clean_line = skill_line.strip()

        # Check if it's a categorized line (e.g., "Programming Languages: Python, Java")
        if ':' in clean_line:
            category, skills_text = clean_line.split(':', 1)
            category_lower = category.lower().strip()
            skills_list = [s.strip() for s in skills_text.split(',') if s.strip()]

            if any(tech_word in category_lower for tech_word in ['programming', 'technical', 'languages', 'frameworks', 'databases']):
                technical_skills.extend(skills_list)
            elif any(tool_word in category_lower for tool_word in ['tools', 'platforms', 'cloud', 'software']):
                tools_skills.extend(skills_list)
            else:
                business_skills.extend(skills_list)
        else:
            # Process as comma-separated skills
            skills_list = [s.strip() for s in clean_line.split(',') if s.strip()]

            # Categorize based on content
            for skill in skills_list:
                skill_lower = skill.lower()
                if any(tech in skill_lower for tech in ['python', 'java', 'javascript', 'sql', 'react', 'node', 'django', 'flask']):
                    technical_skills.append(skill)
                elif any(tool in skill_lower for tool in ['aws', 'azure', 'docker', 'kubernetes', 'git', 'jenkins', 'mongodb']):
                    tools_skills.append(skill)
                elif any(biz in skill_lower for biz in ['management', 'strategy', 'consulting', 'leadership', 'analysis', 'planning']):
                    business_skills.append(skill)
                else:
                    # Default to technical
                    technical_skills.append(skill)

    # Add job-relevant skills
    for keyword in jd_keywords[:5]:
        keyword_title = keyword.title()
        all_skills = technical_skills + business_skills + tools_skills

        if keyword.lower() not in [s.lower() for s in all_skills]:
            if keyword.lower() in ['python', 'sql', 'javascript', 'react', 'java', 'c++', 'machine', 'learning', 'ai']:
                technical_skills.append(keyword_title)
            elif keyword.lower() in ['aws', 'azure', 'docker', 'kubernetes', 'git', 'cloud']:
                tools_skills.append(keyword_title)
            else:
                business_skills.append(keyword_title)

    # Build skills section
    skills_section = "## SKILLS\n"

    if technical_skills:
        skills_section += f"*   **Technical:** {', '.join(technical_skills[:8])}\n"
    if tools_skills:
        skills_section += f"*   **Tools & Platforms:** {', '.join(tools_skills[:8])}\n"
    if business_skills:
        skills_section += f"*   **Business:** {', '.join(business_skills[:8])}\n"

    # Fallback if no skills found
    if not technical_skills and not tools_skills and not business_skills:
        skills_section += "*   **Technical:** Python, SQL, Data Analysis\n"
        skills_section += "*   **Business:** Strategic Planning, Project Management\n"

    return skills_section

def create_languages_section(parsed_languages: list) -> str:
    """
    Create languages section from parsed content or use defaults.
    """
    if parsed_languages:
        languages_section = "## LANGUAGES\n"
        for lang_line in parsed_languages:
            if lang_line.strip():
                languages_section += f"*   {lang_line.strip()}\n"
        return languages_section
    else:
        return """## LANGUAGES
*   English (Professional)
*   Additional languages as applicable
"""

def create_education_section(parsed_education: list) -> str:
    """
    Create education section from parsed content with better formatting.
    """
    if not parsed_education:
        return """## EDUCATION
*   **Relevant Degree**, Institution, Year
*   **Additional Qualifications** as applicable
"""

    education_section = "## EDUCATION\n"

    for edu_line in parsed_education:
        edu_line = edu_line.strip()
        if not edu_line:
            continue

        # Try to format education entries better
        if any(degree in edu_line.lower() for degree in ['bachelor', 'master', 'phd', 'doctorate', 'degree']):
            # This looks like a degree line
            education_section += f"*   **{edu_line}**\n"
        elif any(cert in edu_line.lower() for cert in ['certified', 'certification', 'certificate']):
            # This looks like a certification
            education_section += f"*   {edu_line}\n"
        else:
            # Generic education entry
            education_section += f"*   {edu_line}\n"

    return education_section

def calculate_match_score(job_description: str, resume_content: str) -> int:
    """
    Enhanced match score calculation based on keyword overlap and relevance.
    """
    jd_keywords = set(extract_keywords(job_description, num_keywords=20))
    resume_keywords = set(extract_keywords(resume_content, num_keywords=50))

    common_keywords = jd_keywords.intersection(resume_keywords)

    # Base score from keyword overlap
    base_score = int((len(common_keywords) / len(jd_keywords)) * 100) if len(jd_keywords) > 0 else 0

    # Bonus points for high-value keywords
    high_value_keywords = {'python', 'sql', 'machine', 'learning', 'ai', 'management', 'strategy', 'consulting', 'leadership'}
    high_value_matches = common_keywords.intersection(high_value_keywords)
    bonus_score = len(high_value_matches) * 5

    final_score = min(base_score + bonus_score, 100)
    return final_score


if __name__ == "__main__":
    # Example Usage:
    sample_job_description = """
    We are seeking a highly motivated AI Consultant with strong experience in machine learning, 
    data analysis, and strategic consulting. The ideal candidate will have a proven track record 
    of delivering impactful AI solutions and working with cross-functional teams. 
    Experience in product management and client-facing roles is a plus.
    """

    sample_base_resume_content = """
    Name: Alex Guyenne
    Summary: AI Consultant | Digital Product Strategist | Account Manager. I help companies automate workflows.
    Experience: Gladtobe (AI Consultant), Previous Company (Digital Product Manager)
    Skills: Python, SQL, Product Management
    Education: Master of Science in AI
    """

    sample_key_achievements = [
        "Led development of an AI assistant for a German telecom, automating content creation and reducing delivery time by 60%."
    ]

    result = generate_resume(
        job_description=sample_job_description,
        base_resume_content=sample_base_resume_content,
        key_achievements=sample_key_achievements
    )

    print("--- Tailored Resume (Markdown) ---")
    print(result['tailored_resume_markdown'])
    print("\n--- Match Score & Rationale ---")
    print(result['match_rationale'])
    print("\n--- Strategic Rationale ---")
    print(result['strategic_rationale'])